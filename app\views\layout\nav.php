<?php $auth = new Auth(); ?>

<!-- Desktop Navigation -->
<nav class="bg-white shadow-lg hidden md:block">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="/" class="flex items-center">
                    <i class="fas fa-truck text-blue-600 text-2xl <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                    <span class="text-xl font-bold text-gray-900"><?= $translations['app_name'] ?? 'Tawssil Pro' ?></span>
                </a>
            </div>
            
            <!-- Navigation Links -->
            <div class="flex items-center space-x-4 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                <!-- Language Switcher -->
                <div class="relative">
                    <select class="language-switcher bg-gray-100 border border-gray-300 rounded-md px-3 py-1 text-sm">
                        <option value="ar" <?= $lang === 'ar' ? 'selected' : '' ?>>العربية</option>
                        <option value="en" <?= $lang === 'en' ? 'selected' : '' ?>>English</option>
                        <option value="fr" <?= $lang === 'fr' ? 'selected' : '' ?>>Français</option>
                    </select>
                </div>
                
                <?php if ($auth->check()): ?>
                    <!-- Authenticated User Menu -->
                    <a href="/dashboard" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">
                        <?= $translations['dashboard'] ?? 'Dashboard' ?>
                    </a>
                    
                    <?php if ($_SESSION['user']['role'] === 'driver'): ?>
                        <a href="/trips" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">
                            <?= $translations['trips'] ?? 'Trips' ?>
                        </a>
                        <a href="/map/drivers" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">
                            <?= $translations['map'] ?? 'Map' ?>
                        </a>
                    <?php endif; ?>
                    
                    <?php if ($_SESSION['user']['role'] === 'sender'): ?>
                        <a href="/shipments" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">
                            <?= $translations['shipments'] ?? 'Shipments' ?>
                        </a>
                    <?php endif; ?>
                    
                    <a href="/offers" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">
                        <?= $translations['offers'] ?? 'Offers' ?>
                    </a>
                    
                    <a href="/messages" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium relative">
                        <?= $translations['messages'] ?? 'Messages' ?>
                    </a>
                    
                    <a href="/notifications" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium relative">
                        <?= $translations['notifications'] ?? 'Notifications' ?>
                        <span class="notification-badge absolute -top-1 -<?= $isRTL ? 'left' : 'right' ?>-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden">0</span>
                    </a>
                    
                    <!-- User Dropdown -->
                    <div class="relative">
                        <button class="flex items-center text-gray-700 hover:text-blue-600">
                            <?php if ($_SESSION['user']['avatar']): ?>
                                <img src="/uploads/<?= $_SESSION['user']['avatar'] ?>" alt="Avatar" class="w-8 h-8 rounded-full <?= $isRTL ? 'ml-2' : 'mr-2' ?>">
                            <?php else: ?>
                                <i class="fas fa-user-circle text-2xl <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                            <?php endif; ?>
                            <span><?= $_SESSION['user']['name'] ?></span>
                            <i class="fas fa-chevron-down <?= $isRTL ? 'mr-1' : 'ml-1' ?>"></i>
                        </button>
                        
                        <div class="hidden absolute <?= $isRTL ? 'left' : 'right' ?>-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50" id="user-menu">
                            <a href="/profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <?= $translations['profile'] ?? 'Profile' ?>
                            </a>
                            <form action="/logout" method="POST" class="block">
                                <input type="hidden" name="csrf_token" value="<?= $auth->generateCsrf() ?>">
                                <button type="submit" class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <?= $translations['logout'] ?? 'Logout' ?>
                                </button>
                            </form>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Guest Menu -->
                    <a href="/login" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">
                        <?= $translations['login'] ?? 'Login' ?>
                    </a>
                    <a href="/register" class="bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-md text-sm font-medium">
                        <?= $translations['register'] ?? 'Register' ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</nav>

<!-- Mobile Navigation -->
<?php if ($auth->check()): ?>
<nav class="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
    <div class="flex justify-around py-2">
        <a href="/dashboard" class="flex flex-col items-center py-2 px-3 text-gray-600 hover:text-blue-600">
            <i class="fas fa-home text-xl"></i>
            <span class="text-xs mt-1"><?= $translations['home'] ?? 'Home' ?></span>
        </a>
        
        <?php if ($_SESSION['user']['role'] === 'driver'): ?>
            <a href="/trips" class="flex flex-col items-center py-2 px-3 text-gray-600 hover:text-blue-600">
                <i class="fas fa-route text-xl"></i>
                <span class="text-xs mt-1"><?= $translations['trips'] ?? 'Trips' ?></span>
            </a>
        <?php else: ?>
            <a href="/shipments" class="flex flex-col items-center py-2 px-3 text-gray-600 hover:text-blue-600">
                <i class="fas fa-box text-xl"></i>
                <span class="text-xs mt-1"><?= $translations['shipments'] ?? 'Shipments' ?></span>
            </a>
        <?php endif; ?>
        
        <a href="/offers" class="flex flex-col items-center py-2 px-3 text-gray-600 hover:text-blue-600">
            <i class="fas fa-handshake text-xl"></i>
            <span class="text-xs mt-1"><?= $translations['offers'] ?? 'Offers' ?></span>
        </a>
        
        <a href="/messages" class="flex flex-col items-center py-2 px-3 text-gray-600 hover:text-blue-600 relative">
            <i class="fas fa-comments text-xl"></i>
            <span class="text-xs mt-1"><?= $translations['messages'] ?? 'Messages' ?></span>
        </a>
        
        <a href="/profile" class="flex flex-col items-center py-2 px-3 text-gray-600 hover:text-blue-600">
            <i class="fas fa-user text-xl"></i>
            <span class="text-xs mt-1"><?= $translations['profile'] ?? 'Profile' ?></span>
        </a>
    </div>
</nav>
<?php endif; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // User menu toggle
    const userMenuButton = document.querySelector('[data-user-menu-button]') || document.querySelector('button:has(i.fa-user-circle)');
    const userMenu = document.getElementById('user-menu');
    
    if (userMenuButton && userMenu) {
        userMenuButton.addEventListener('click', function(e) {
            e.stopPropagation();
            userMenu.classList.toggle('hidden');
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function() {
            userMenu.classList.add('hidden');
        });
    }
    
    // Language switcher
    const languageSwitcher = document.querySelector('.language-switcher');
    if (languageSwitcher) {
        languageSwitcher.addEventListener('change', function() {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/set-language';
            
            const langInput = document.createElement('input');
            langInput.type = 'hidden';
            langInput.name = 'lang';
            langInput.value = this.value;
            
            const redirectInput = document.createElement('input');
            redirectInput.type = 'hidden';
            redirectInput.name = 'redirect';
            redirectInput.value = window.location.pathname;
            
            form.appendChild(langInput);
            form.appendChild(redirectInput);
            document.body.appendChild(form);
            form.submit();
        });
    }
});
</script>
