<?php
// Minimal test to isolate the issue

echo "<h1>Minimal Router Test</h1>";

// Test 1: Basic PHP
echo "<p>✅ PHP working</p>";

// Test 2: Load config
try {
    require_once __DIR__ . '/../app/config/config.php';
    echo "<p>✅ Config loaded</p>";
    echo "<p>Debug mode: " . (Config::get('app.debug') ? 'ON' : 'OFF') . "</p>";
} catch (Exception $e) {
    echo "<p>❌ Config error: " . $e->getMessage() . "</p>";
    exit;
}

// Test 3: Load router
try {
    require_once __DIR__ . '/../app/core/Router.php';
    echo "<p>✅ Router class loaded</p>";
} catch (Exception $e) {
    echo "<p>❌ Router error: " . $e->getMessage() . "</p>";
    exit;
}

// Test 4: Create router instance
try {
    $router = new Router();
    echo "<p>✅ Router instance created</p>";
} catch (Exception $e) {
    echo "<p>❌ Router instance error: " . $e->getMessage() . "</p>";
    exit;
}

// Test 5: Add a simple route
try {
    $router->get('/', function() {
        echo "<h2>🎉 HOME ROUTE WORKS!</h2>";
    });
    echo "<p>✅ Route added</p>";
} catch (Exception $e) {
    echo "<p>❌ Route error: " . $e->getMessage() . "</p>";
    exit;
}

// Test 6: Show request info
echo "<h2>Request Info</h2>";
echo "<ul>";
echo "<li>REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</li>";
echo "<li>REQUEST_METHOD: " . ($_SERVER['REQUEST_METHOD'] ?? 'Not set') . "</li>";
echo "<li>SCRIPT_NAME: " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</li>";
echo "</ul>";

// Test 7: Process path like the main app
$requestUri = $_SERVER['REQUEST_URI'] ?? '/';
$path = parse_url($requestUri, PHP_URL_PATH);

echo "<h2>Path Processing</h2>";
echo "<ul>";
echo "<li>Original URI: {$requestUri}</li>";
echo "<li>Parsed path: {$path}</li>";

// Remove project directory
$basePath = '/TawssilWeb/public';
if (strpos($path, $basePath) === 0) {
    $path = substr($path, strlen($basePath));
    echo "<li>After removing base: {$path}</li>";
}

// Ensure starts with /
if (empty($path)) {
    $path = '/';
} elseif ($path[0] !== '/') {
    $path = '/' . $path;
}

echo "<li>Final path: {$path}</li>";
echo "</ul>";

// Test 8: Override REQUEST_URI and dispatch
$_SERVER['REQUEST_URI'] = $path;

echo "<h2>Dispatch Test</h2>";
echo "<p>Attempting to dispatch with path: {$path}</p>";

try {
    $router->dispatch();
    echo "<p>✅ Dispatch completed</p>";
} catch (Exception $e) {
    echo "<p>❌ Dispatch error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
