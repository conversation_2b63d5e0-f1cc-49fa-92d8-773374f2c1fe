<?php
// Simplified index.php for debugging
echo "<h1>Simple Index Test</h1>";

// Step 1: Test basic PHP
echo "<p>✅ PHP is working</p>";

// Step 2: Test config loading
try {
    require_once __DIR__ . '/../app/config/config.php';
    echo "<p>✅ Config loaded</p>";
} catch (Exception $e) {
    echo "<p>❌ Config failed: " . $e->getMessage() . "</p>";
    exit;
}

// Step 3: Test helpers
try {
    require_once __DIR__ . '/../app/helpers.php';
    echo "<p>✅ Helpers loaded</p>";
} catch (Exception $e) {
    echo "<p>❌ Helpers failed: " . $e->getMessage() . "</p>";
    exit;
}

// Step 4: Test router loading
try {
    require_once __DIR__ . '/../app/core/Router.php';
    echo "<p>✅ Router class loaded</p>";
} catch (Exception $e) {
    echo "<p>❌ Router failed: " . $e->getMessage() . "</p>";
    exit;
}

// Step 5: Test router creation
try {
    $router = new Router();
    echo "<p>✅ Router instance created</p>";
} catch (Exception $e) {
    echo "<p>❌ Router creation failed: " . $e->getMessage() . "</p>";
    exit;
}

// Step 6: Test simple route
try {
    $router->get('/', function() {
        echo "<h2>✅ Home route working!</h2>";
    });
    
    $router->get('/test', function() {
        echo "<h2>✅ Test route working!</h2>";
    });
    
    echo "<p>✅ Routes defined</p>";
} catch (Exception $e) {
    echo "<p>❌ Route definition failed: " . $e->getMessage() . "</p>";
    exit;
}

// Step 7: Show current request info
echo "<h2>Request Information</h2>";
$requestUri = $_SERVER['REQUEST_URI'] ?? '';
$method = $_SERVER['REQUEST_METHOD'] ?? '';
echo "<ul>";
echo "<li>Method: {$method}</li>";
echo "<li>URI: {$requestUri}</li>";
echo "<li>Parsed path: " . parse_url($requestUri, PHP_URL_PATH) . "</li>";
echo "</ul>";

// Step 8: Test dispatch
echo "<h2>Dispatch Test</h2>";
try {
    echo "<p>Attempting to dispatch...</p>";
    $router->dispatch();
    echo "<p>✅ Dispatch completed</p>";
} catch (Exception $e) {
    echo "<p>❌ Dispatch failed: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
