You are an expert full‑stack engineer. Build a production‑ready, responsive web app called **tawssil pro** using:
- Backend: Native PHP 8.x (no frameworks), organized in MVC-ish structure.
- Frontend: HTML + TailwindCSS (via CDN) + vanilla JavaScript (ES6, Fetch/AJAX).
- Database: MySQL 8.x.
- Server: Apache with URL rewriting (.htaccess).

## App concept
A marketplace that connects drivers traveling between cities with people who want to send goods. Roles: **driver**, **sender**, **admin**.

## Core features
1) Auth (register/login/logout) with PHP sessions, password_hash/password_verify, role-based access.
2) Drivers: CRUD trips (from_city, to_city, date, capacity_kg, price_per_kg, vehicle_type, notes).
3) Senders: CRUD shipments (from_city, to_city, ready_date, weight_kg, description, fragile boolean, photos optional).
4) Matching & Search:
   - Senders search trips by route/date/price.
   - Drivers search shipments by route/date/weight.
   - Relevance score (same route first, nearby dates next).
5) Offers/Bookings:
   - Sender can request to book a trip.
   - Driver can offer to carry a shipment.
   - Offer statuses: pending → accepted/rejected/cancelled.
6) Messaging:
   - Simple 1‑to‑1 inbox per offer/trip/shipment (subject, text, attachments optional).
7) Notifications:
   - In‑app notifications (unread counts) for new offer, status change, new message (polling every 20s).
8) Profiles:
   - Edit profile, phone, avatar, vehicle info for drivers, ID verification placeholder.
9) Admin:
   - Dashboard to manage users, trips, shipments, offers; soft-delete/ban user.
10) i18n & RTL:
   - UI defaults to Arabic (RTL), with switcher for French and English.
   - Implement a simple translation loader using PHP arrays in /app/lang/{ar,fr,en}.php.
11) Security:
   - CSRF tokens on all forms.
   - Server-side validation & file upload hardening (MIME/size).
   - Prepared statements (PDO) only.
12) UX:
   - Fully responsive (mobile-first) with Tailwind components.
   - Reusable components: header, footer, nav, toasts, modals.
13) Seed data:
   - Create seed users (driver/sender/admin), trips, shipments, sample messages/offers for demo.
14) README:
   - Clear setup steps, DB import, env config, demo logins.

## Project structure (create all files)
/
├─ public/
│  ├─ index.php                # Front controller (handles routing)
│  ├─ assets/
│  │  ├─ css/tailwind.css      # (optional) or use CDN
│  │  └─ js/app.js
│  ├─ uploads/                 # user uploads (avatars, shipment photos)
│  └─ .htaccess                # URL rewriting to index.php
├─ app/
│  ├─ config/
│  │  ├─ config.php            # env loader
│  │  └─ database.php          # PDO connection
│  ├─ core/
│  │  ├─ Router.php
│  │  ├─ Controller.php
│  │  └─ Auth.php              # session helpers, guards, csrf
│  ├─ controllers/
│  │  ├─ AuthController.php
│  │  ├─ TripController.php
│  │  ├─ ShipmentController.php
│  │  ├─ OfferController.php
│  │  ├─ MessageController.php
│  │  ├─ NotificationController.php
│  │  ├─ ProfileController.php
│  │  └─ AdminController.php
│  ├─ models/
│  │  ├─ User.php
│  │  ├─ Trip.php
│  │  ├─ Shipment.php
│  │  ├─ Offer.php
│  │  ├─ Message.php
│  │  └─ Notification.php
│  └─ views/
│     ├─ layout/
│     │  ├─ header.php  # includes RTL meta, lang switcher
│     │  ├─ nav.php
│     │  └─ footer.php
│     ├─ home.php
│     ├─ auth/{login.php, register.php}
│     ├─ dashboard/{driver.php, sender.php, admin.php}
│     ├─ trips/{index.php, create.php, edit.php, show.php}
│     ├─ shipments/{index.php, create.php, edit.php, show.php}
│     ├─ offers/{index.php, show.php}
│     ├─ messages/{inbox.php, thread.php}
│     ├─ notifications/index.php
│     └─ profile/{show.php, edit.php}
├─ app/lang/
│  ├─ ar.php  # Arabic labels (default, RTL)
│  ├─ fr.php
│  └─ en.php
├─ app/helpers.php
├─ scripts/
│  ├─ migrate.sql
│  └─ seed.sql
├─ .env.example
├─ README.md
└─ composer.json  # if you use vlucas/phpdotenv (optional)

## Routing
- Use a simple Router that maps GET/POST to controllers, e.g.:
  / → Home, /login, /register, /logout
  /dashboard
  /trips (GET list), /trips/create (GET/POST), /trips/{id} (GET), /trips/{id}/edit (GET/POST), /trips/{id}/delete (POST)
  /shipments ... (same pattern)
  /offers (list), /offers/{id} (show), /offers/{id}/accept (POST), /reject (POST), /cancel (POST)
  /messages (inbox), /messages/thread/{id}, /messages/send (POST)
  /notifications (list), /notifications/read (POST)
  /profile (GET), /profile/edit (GET/POST)
- Enforce role guards (driver/sender/admin) per route.

## Database (create scripts in scripts/migrate.sql)
Tables (with indexes & FKs):
- users(id PK, name, email UNIQUE, phone, password_hash, role ENUM('driver','sender','admin'), avatar, vehicle_type, vehicle_plate, created_at, updated_at, deleted_at NULL)
- trips(id PK, user_id FK→users, from_city, to_city, trip_date DATE, capacity_kg DECIMAL(6,2), price_per_kg DECIMAL(8,2), notes TEXT, status ENUM('open','full','cancelled') DEFAULT 'open', created_at, updated_at)
- shipments(id PK, user_id FK→users, from_city, to_city, ready_date DATE, weight_kg DECIMAL(6,2), description TEXT, fragile TINYINT(1) DEFAULT 0, photo VARCHAR(255) NULL, status ENUM('open','matched','delivered','cancelled') DEFAULT 'open', created_at, updated_at)
- offers(id PK, trip_id FK→trips NULL, shipment_id FK→shipments NULL, proposer_id FK→users, receiver_id FK→users, price_total DECIMAL(10,2) NULL, status ENUM('pending','accepted','rejected','cancelled') DEFAULT 'pending', created_at, updated_at)
- messages(id PK, offer_id FK→offers, sender_id FK→users, receiver_id FK→users, body TEXT, attachment VARCHAR(255) NULL, created_at)
- notifications(id PK, user_id FK→users, type VARCHAR(50), ref_id INT NULL, seen TINYINT(1) DEFAULT 0, created_at)
Indexes: (from_city,to_city,trip_date) on trips; (from_city,to_city,ready_date) on shipments; composite indexes on offers(receiver_id,status), messages(offer_id,created_at).

## Seed data (scripts/seed.sql)
- 1 admin, 2 drivers, 2 senders with known passwords (e.g., Admin@123, Driver@123, Sender@123).
- 5 trips, 5 shipments diversified by cities/dates.
- 3 offers (pending/accepted), a few messages per offer.

## Frontend requirements
- Use Tailwind via CDN, set `dir="rtl"` when Arabic is active. Add a language switch that flips to LTR for en/fr and reloads labels from app/lang/*.php.
- Mobile-first pages with clean cards, sticky bottom nav on mobile (Home, Search, Post, Inbox, Profile).
- Forms submit via Fetch (AJAX) to JSON endpoints; show success/error toasts.
- Search page combines filters (from_city, to_city, date range, max price, capacity/weight) and shows match score badge.

## Backend details
- PDO with prepared statements; wrap DB calls in Models.
- CSRF: generate token in session, include hidden input in forms, verify server-side.
- File uploads: restrict to images (jpg/png/webp), max 2 MB, random filenames in /public/uploads.
- Notifications: on new offer / message / status change create a notification; implement `/notifications/unread-count` polled every 20s.
- Access control: Only owner or admin can edit/delete their resources.

## Example endpoints (return JSON for AJAX)
- POST /api/auth/login, /api/auth/register
- GET /api/trips?from=...&to=...&date=...&price_max=...
- POST /api/trips (create), PUT /api/trips/{id}, DELETE /api/trips/{id}
- GET /api/shipments ...
- POST /api/offers (create), POST /api/offers/{id}/{accept|reject|cancel}
- GET /api/messages/thread/{offer_id}, POST /api/messages/send
- GET /api/notifications/unread-count, POST /api/notifications/mark-read

## Views (key screens to implement now)
- Home: hero, quick search, CTA “Post Trip” / “Post Shipment”.
- Auth: login/register with role selector.
- Dashboard (driver): next trips, incoming requests, earnings placeholder.
- Dashboard (sender): my shipments, offers status.
- Listing + create/edit forms for trips & shipments.
- Offers: list + details with action buttons.
- Messaging: inbox + thread with composer.
- Profile: show/edit, upload avatar, for drivers vehicle info.

## README
- How to run locally (Apache + PHP 8, MySQL).
- Create DB, import migrate.sql then seed.sql.
- Copy .env.example to .env and set DB creds.
- Default logins and roles.
- Notes on security & deployment.

Deliver the full project with working PHP pages, controllers, models, migrations, seeds, and sample data so it runs immediately after DB import.
