modules = ["php"]
[agent]
expertMode = true

[nix]
channel = "stable-25_05"

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "PHP Server"

[[workflows.workflow]]
name = "PHP Server"
author = "agent"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "cd public && php -S 0.0.0.0:5000"
waitForPort = 5000

[workflows.workflow.metadata]
outputType = "webview"
