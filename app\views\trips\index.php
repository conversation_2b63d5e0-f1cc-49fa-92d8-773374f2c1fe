<?php 
$title = $translations['trips'] ?? 'Trips - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900"><?= $translations['trips'] ?? 'Trips' ?></h1>
                <p class="text-gray-600 mt-2"><?= $translations['find_available_trips'] ?? 'Find available trips for your shipments' ?></p>
            </div>
            <?php if ($auth->hasRole('driver')): ?>
                <a href="/trips/create" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition duration-300">
                    <i class="fas fa-plus <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                    <?= $translations['post_trip'] ?? 'Post Trip' ?>
                </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Search Filters -->
    <div class="bg-white p-6 rounded-lg shadow mb-8">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2"><?= $translations['from_city'] ?? 'From City' ?></label>
                <input type="text" name="from_city" value="<?= htmlspecialchars($_GET['from_city'] ?? '') ?>" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="<?= $translations['enter_from_city'] ?? 'Enter departure city' ?>">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2"><?= $translations['to_city'] ?? 'To City' ?></label>
                <input type="text" name="to_city" value="<?= htmlspecialchars($_GET['to_city'] ?? '') ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="<?= $translations['enter_to_city'] ?? 'Enter destination city' ?>">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2"><?= $translations['date'] ?? 'Date' ?></label>
                <input type="date" name="date" value="<?= htmlspecialchars($_GET['date'] ?? '') ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2"><?= $translations['max_price'] ?? 'Max Price/kg' ?></label>
                <input type="number" name="max_price" value="<?= htmlspecialchars($_GET['max_price'] ?? '') ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="MAD">
            </div>
            
            <div class="md:col-span-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md transition duration-300">
                    <i class="fas fa-search <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                    <?= $translations['search'] ?? 'Search' ?>
                </button>
                <a href="/trips" class="<?= $isRTL ? 'mr-4' : 'ml-4' ?> text-gray-600 hover:text-gray-800">
                    <?= $translations['clear_filters'] ?? 'Clear Filters' ?>
                </a>
            </div>
        </form>
    </div>

    <!-- Trips List -->
    <div class="space-y-6">
        <?php if (empty($trips)): ?>
            <div class="bg-white rounded-lg shadow p-8 text-center">
                <i class="fas fa-route text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2"><?= $translations['no_trips_found'] ?? 'No trips found' ?></h3>
                <p class="text-gray-600"><?= $translations['try_different_search'] ?? 'Try adjusting your search criteria or check back later.' ?></p>
                <?php if ($auth->hasRole('driver')): ?>
                    <a href="/trips/create" class="mt-4 inline-block bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md transition duration-300">
                        <?= $translations['post_first_trip'] ?? 'Post Your First Trip' ?>
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <?php foreach ($trips as $trip): ?>
                <div class="bg-white rounded-lg shadow hover:shadow-lg transition duration-300">
                    <div class="p-6">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center mb-3">
                                    <div class="flex items-center text-lg font-semibold text-gray-900">
                                        <i class="fas fa-map-marker-alt text-green-600 <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                                        <?= htmlspecialchars($trip['from_city']) ?>
                                        <i class="fas fa-arrow-<?= $isRTL ? 'left' : 'right' ?> text-gray-400 mx-3"></i>
                                        <i class="fas fa-map-marker-alt text-red-600 <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                                        <?= htmlspecialchars($trip['to_city']) ?>
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                                    <div>
                                        <span class="text-sm text-gray-500"><?= $translations['date'] ?? 'Date' ?>:</span>
                                        <p class="font-medium"><?= date('M d, Y', strtotime($trip['trip_date'])) ?></p>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-500"><?= $translations['capacity'] ?? 'Capacity' ?>:</span>
                                        <p class="font-medium"><?= $trip['capacity_kg'] ?> kg</p>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-500"><?= $translations['price_per_kg'] ?? 'Price/kg' ?>:</span>
                                        <p class="font-medium text-green-600"><?= $trip['price_per_kg'] ?> MAD</p>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-500"><?= $translations['driver'] ?? 'Driver' ?>:</span>
                                        <p class="font-medium"><?= htmlspecialchars($trip['driver_name']) ?></p>
                                    </div>
                                </div>
                                
                                <?php if ($trip['notes']): ?>
                                    <div class="mb-4">
                                        <span class="text-sm text-gray-500"><?= $translations['notes'] ?? 'Notes' ?>:</span>
                                        <p class="text-gray-700 mt-1"><?= htmlspecialchars($trip['notes']) ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="<?= $isRTL ? 'mr-4' : 'ml-4' ?> text-<?= $isRTL ? 'left' : 'right' ?>">
                                <span class="px-3 py-1 text-sm font-medium rounded-full <?= 
                                    $trip['status'] === 'open' ? 'bg-green-100 text-green-800' : 
                                    ($trip['status'] === 'full' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800')
                                ?>">
                                    <?= $translations['status_' . $trip['status']] ?? ucfirst($trip['status']) ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
                            <div class="text-sm text-gray-500">
                                <i class="fas fa-phone <?= $isRTL ? 'ml-1' : 'mr-1' ?>"></i>
                                <?= htmlspecialchars($trip['driver_phone']) ?>
                            </div>
                            
                            <div class="flex space-x-3 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                                <a href="/trips/<?= $trip['id'] ?>" class="text-blue-600 hover:text-blue-500 font-medium">
                                    <?= $translations['view_details'] ?? 'View Details' ?>
                                </a>
                                
                                <?php if ($auth->hasRole('sender') && $trip['status'] === 'open'): ?>
                                    <button onclick="makeOffer(<?= $trip['id'] ?>)" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-300">
                                        <?= $translations['make_offer'] ?? 'Make Offer' ?>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Make Offer Modal -->
<div id="offerModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['make_offer'] ?? 'Make Offer' ?></h3>
            
            <form id="offerForm" class="ajax-form" action="/offers/create" method="POST">
                <input type="hidden" id="offerTripId" name="trip_id">
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2"><?= $translations['total_price'] ?? 'Total Price (MAD)' ?></label>
                    <input type="number" name="price_total" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="<?= $translations['enter_price'] ?? 'Enter your offer price' ?>">
                </div>
                
                <div class="flex justify-center space-x-4 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                    <button type="button" onclick="closeOfferModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        <?= $translations['cancel'] ?? 'Cancel' ?>
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        <?= $translations['send_offer'] ?? 'Send Offer' ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function makeOffer(tripId) {
    document.getElementById('offerTripId').value = tripId;
    document.getElementById('offerModal').classList.remove('hidden');
}

function closeOfferModal() {
    document.getElementById('offerModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('offerModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeOfferModal();
    }
});
</script>

<?php include __DIR__ . '/../layout/footer.php'; ?>
