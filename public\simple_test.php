<?php
// Simple test without routing
require_once __DIR__ . '/../app/config/config.php';
require_once __DIR__ . '/../app/helpers.php';

echo "<h1>Tawssil Pro - Simple Test</h1>";
echo "<p>✅ PHP is working</p>";
echo "<p>✅ Configuration loaded</p>";
echo "<p>✅ Helpers loaded</p>";

try {
    require_once __DIR__ . '/../app/config/database.php';
    $db = Database::getInstance();
    echo "<p>✅ Database connection working</p>";
} catch (Exception $e) {
    echo "<p>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

echo "<h2>Next Steps:</h2>";
echo "<ol>";
echo "<li>If database failed, start MySQL in XAMPP and run migration script</li>";
echo "<li>Try accessing: <a href='index.php'>index.php</a></li>";
echo "<li>Try accessing: <a href='debug.php'>debug.php</a></li>";
echo "</ol>";
?>
