<?php
// Most basic test - no includes, just PHP
echo "<h1>Basic PHP Test</h1>";
echo "<p>If you see this, P<PERSON> is working!</p>";
echo "<p>Current time: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>PHP Version: " . phpversion() . "</p>";

// Test server variables
echo "<h2>Server Info</h2>";
echo "<ul>";
echo "<li>REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</li>";
echo "<li>SCRIPT_NAME: " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</li>";
echo "<li>HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? 'Not set') . "</li>";
echo "<li>DOCUMENT_ROOT: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Not set') . "</li>";
echo "</ul>";

// Test file paths
echo "<h2>File System Test</h2>";
echo "<ul>";
echo "<li>Current directory: " . __DIR__ . "</li>";
echo "<li>Parent directory: " . dirname(__DIR__) . "</li>";
echo "<li>Config file exists: " . (file_exists(__DIR__ . '/../app/config/config.php') ? 'YES' : 'NO') . "</li>";
echo "<li>.htaccess exists: " . (file_exists(__DIR__ . '/.htaccess') ? 'YES' : 'NO') . "</li>";
echo "</ul>";

// Test if we can include config
echo "<h2>Config Test</h2>";
try {
    require_once __DIR__ . '/../app/config/config.php';
    echo "<p style='color: green;'>✅ Config loaded successfully</p>";
    echo "<p>App URL: " . Config::get('app.url') . "</p>";
    echo "<p>Debug mode: " . (Config::get('app.debug') ? 'ON' : 'OFF') . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Config failed: " . $e->getMessage() . "</p>";
}

echo "<h2>Next Steps</h2>";
echo "<p>1. If this page loads, PHP is working</p>";
echo "<p>2. Try: <a href='index.php'>index.php</a></p>";
echo "<p>3. Try: <a href='../public/'>Go back to public folder</a></p>";
?>
