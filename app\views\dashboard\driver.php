<?php 
$title = $translations['driver_dashboard'] ?? 'Driver Dashboard - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';

// Get driver statistics
$userId = $auth->id();
$tripModel = new Trip();
$userTrips = $tripModel->getByUser($userId);

$stats = [
    'active_trips' => count(array_filter($userTrips, fn($trip) => $trip['status'] === 'open')),
    'completed_trips' => count(array_filter($userTrips, fn($trip) => $trip['status'] === 'full')),
    'total_trips' => count($userTrips)
];

// Get recent offers
$offerModel = new Offer();
$recentOffers = array_slice($offerModel->getByUser($userId), 0, 5);
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">
            <?= $translations['welcome_driver'] ?? 'Welcome' ?>, <?= $_SESSION['user']['name'] ?>!
        </h1>
        <p class="text-gray-600">
            <?= $translations['driver_dashboard_subtitle'] ?? 'Manage your trips and track your transportation business.' ?>
        </p>
    </div>
    
    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <a href="/trips/create" class="bg-blue-600 hover:bg-blue-700 text-white p-6 rounded-lg text-center transition duration-300">
            <i class="fas fa-plus-circle text-3xl mb-2"></i>
            <h3 class="text-lg font-semibold"><?= $translations['post_new_trip'] ?? 'Post New Trip' ?></h3>
        </a>
        
        <a href="/shipments" class="bg-green-600 hover:bg-green-700 text-white p-6 rounded-lg text-center transition duration-300">
            <i class="fas fa-search text-3xl mb-2"></i>
            <h3 class="text-lg font-semibold"><?= $translations['find_shipments'] ?? 'Find Shipments' ?></h3>
        </a>
        
        <a href="/map/drivers" class="bg-purple-600 hover:bg-purple-700 text-white p-6 rounded-lg text-center transition duration-300">
            <i class="fas fa-map-marked-alt text-3xl mb-2"></i>
            <h3 class="text-lg font-semibold"><?= $translations['driver_map'] ?? 'Driver Map' ?></h3>
        </a>
    </div>
    
    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-route text-2xl text-blue-600"></i>
                </div>
                <div class="<?= $isRTL ? 'mr-4' : 'ml-4' ?>">
                    <p class="text-sm font-medium text-gray-500"><?= $translations['active_trips'] ?? 'Active Trips' ?></p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['active_trips'] ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-2xl text-green-600"></i>
                </div>
                <div class="<?= $isRTL ? 'mr-4' : 'ml-4' ?>">
                    <p class="text-sm font-medium text-gray-500"><?= $translations['completed_trips'] ?? 'Completed Trips' ?></p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['completed_trips'] ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-chart-line text-2xl text-purple-600"></i>
                </div>
                <div class="<?= $isRTL ? 'mr-4' : 'ml-4' ?>">
                    <p class="text-sm font-medium text-gray-500"><?= $translations['total_trips'] ?? 'Total Trips' ?></p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['total_trips'] ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Trips -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900"><?= $translations['recent_trips'] ?? 'Recent Trips' ?></h2>
                    <a href="/trips" class="text-blue-600 hover:text-blue-500 text-sm font-medium">
                        <?= $translations['view_all'] ?? 'View All' ?>
                    </a>
                </div>
            </div>
            
            <div class="p-6">
                <?php if (empty($userTrips)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-route text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500"><?= $translations['no_trips_yet'] ?? 'No trips posted yet' ?></p>
                        <a href="/trips/create" class="mt-2 inline-block text-blue-600 hover:text-blue-500">
                            <?= $translations['post_first_trip'] ?? 'Post your first trip' ?>
                        </a>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach (array_slice($userTrips, 0, 3) as $trip): ?>
                            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="font-medium text-gray-900">
                                            <?= htmlspecialchars($trip['from_city']) ?> → <?= htmlspecialchars($trip['to_city']) ?>
                                        </h3>
                                        <p class="text-sm text-gray-500">
                                            <?= date('M d, Y', strtotime($trip['trip_date'])) ?>
                                        </p>
                                        <p class="text-sm text-gray-600">
                                            <?= $trip['capacity_kg'] ?>kg • <?= $trip['price_per_kg'] ?> MAD/kg
                                        </p>
                                    </div>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full <?= 
                                        $trip['status'] === 'open' ? 'bg-green-100 text-green-800' : 
                                        ($trip['status'] === 'full' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800')
                                    ?>">
                                        <?= ucfirst($trip['status']) ?>
                                    </span>
                                </div>
                                <div class="mt-3">
                                    <a href="/trips/<?= $trip['id'] ?>" class="text-blue-600 hover:text-blue-500 text-sm">
                                        <?= $translations['view_details'] ?? 'View Details' ?>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Recent Offers -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900"><?= $translations['recent_offers'] ?? 'Recent Offers' ?></h2>
                    <a href="/offers" class="text-blue-600 hover:text-blue-500 text-sm font-medium">
                        <?= $translations['view_all'] ?? 'View All' ?>
                    </a>
                </div>
            </div>
            
            <div class="p-6">
                <?php if (empty($recentOffers)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-handshake text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500"><?= $translations['no_offers_yet'] ?? 'No offers received yet' ?></p>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($recentOffers as $offer): ?>
                            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="font-medium text-gray-900">
                                            <?= htmlspecialchars($offer['route']) ?>
                                        </h3>
                                        <p class="text-sm text-gray-500">
                                            <?= $translations['from'] ?? 'From' ?>: <?= htmlspecialchars($offer['proposer_name']) ?>
                                        </p>
                                        <?php if ($offer['price_total']): ?>
                                            <p class="text-sm text-gray-600">
                                                <?= $offer['price_total'] ?> MAD
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full <?= 
                                        $offer['status'] === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                                        ($offer['status'] === 'accepted' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800')
                                    ?>">
                                        <?= ucfirst($offer['status']) ?>
                                    </span>
                                </div>
                                <div class="mt-3">
                                    <a href="/offers/<?= $offer['id'] ?>" class="text-blue-600 hover:text-blue-500 text-sm">
                                        <?= $translations['view_offer'] ?? 'View Offer' ?>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../layout/footer.php'; ?>
