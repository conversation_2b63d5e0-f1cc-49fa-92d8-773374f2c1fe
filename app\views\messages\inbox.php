<?php 
$title = $translations['messages'] ?? 'Messages - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900"><?= $translations['messages'] ?? 'Messages' ?></h1>
        <p class="text-gray-600 mt-2"><?= $translations['manage_conversations'] ?? 'Manage your conversations and communications' ?></p>
    </div>

    <!-- Messages List -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">
                <?= $translations['conversations'] ?? 'Conversations' ?> (<?= count($conversations) ?>)
            </h2>
        </div>
        
        <div class="divide-y divide-gray-200">
            <?php if (empty($conversations)): ?>
                <div class="px-6 py-12 text-center">
                    <i class="fas fa-comments text-4xl text-gray-300 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2"><?= $translations['no_conversations'] ?? 'No conversations yet' ?></h3>
                    <p class="text-gray-600 mb-6"><?= $translations['no_conversations_description'] ?? 'Start making offers to begin conversations with other users.' ?></p>
                    
                    <div class="flex justify-center space-x-4 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                        <?php if ($auth->hasRole('driver')): ?>
                            <a href="/shipments" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium">
                                <?= $translations['find_shipments'] ?? 'Find Shipments' ?>
                            </a>
                        <?php else: ?>
                            <a href="/trips" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md font-medium">
                                <?= $translations['find_trips'] ?? 'Find Trips' ?>
                            </a>
                        <?php endif; ?>
                        
                        <a href="/offers" class="border border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-2 rounded-md font-medium">
                            <?= $translations['view_offers'] ?? 'View Offers' ?>
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($conversations as $conversation): ?>
                    <div class="px-6 py-4 hover:bg-gray-50 transition duration-200">
                        <a href="/messages/thread/<?= $conversation['offer_id'] ?>" class="block">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center flex-1 min-w-0">
                                    <!-- Avatar -->
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                                            <i class="fas fa-user text-gray-500"></i>
                                        </div>
                                    </div>
                                    
                                    <!-- Conversation Info -->
                                    <div class="<?= $isRTL ? 'mr-4' : 'ml-4' ?> flex-1 min-w-0">
                                        <div class="flex items-center justify-between mb-1">
                                            <h3 class="text-sm font-medium text-gray-900 truncate">
                                                <?= htmlspecialchars($conversation['other_user_name']) ?>
                                            </h3>
                                            
                                            <?php if ($conversation['unread_count'] > 0): ?>
                                                <span class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> bg-blue-600 text-white text-xs font-medium px-2 py-1 rounded-full">
                                                    <?= $conversation['unread_count'] ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <p class="text-sm text-gray-600 truncate mb-1">
                                            <?= htmlspecialchars($conversation['subject']) ?>
                                        </p>
                                        
                                        <?php if ($conversation['last_message']): ?>
                                            <p class="text-sm text-gray-500 truncate">
                                                <?= htmlspecialchars(substr($conversation['last_message'], 0, 50)) ?>
                                                <?= strlen($conversation['last_message']) > 50 ? '...' : '' ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <!-- Timestamp -->
                                <div class="<?= $isRTL ? 'mr-4' : 'ml-4' ?> flex-shrink-0 text-<?= $isRTL ? 'left' : 'right' ?>">
                                    <?php if ($conversation['last_message_date']): ?>
                                        <span class="text-xs text-gray-500">
                                            <?php
                                            $messageDate = strtotime($conversation['last_message_date']);
                                            $today = strtotime('today');
                                            $yesterday = strtotime('yesterday');
                                            
                                            if ($messageDate >= $today) {
                                                echo date('H:i', $messageDate);
                                            } elseif ($messageDate >= $yesterday) {
                                                echo $translations['yesterday'] ?? 'Yesterday';
                                            } else {
                                                echo date('M d', $messageDate);
                                            }
                                            ?>
                                        </span>
                                    <?php endif; ?>
                                    
                                    <div class="mt-1">
                                        <i class="fas fa-chevron-<?= $isRTL ? 'left' : 'right' ?> text-gray-400 text-xs"></i>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Help Section -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex items-start">
            <i class="fas fa-info-circle text-blue-600 <?= $isRTL ? 'ml-3' : 'mr-3' ?> mt-1"></i>
            <div>
                <h3 class="text-sm font-medium text-blue-800 mb-2"><?= $translations['messaging_tips'] ?? 'Messaging Tips' ?></h3>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li>• <?= $translations['tip_1'] ?? 'Be clear and professional in your communications' ?></li>
                    <li>• <?= $translations['tip_2'] ?? 'Respond promptly to maintain good relationships' ?></li>
                    <li>• <?= $translations['tip_3'] ?? 'Use messages to coordinate pickup and delivery details' ?></li>
                    <li>• <?= $translations['tip_4'] ?? 'Keep records of important agreements and arrangements' ?></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../layout/footer.php'; ?>
