# Tawssil Pro - Logistics Marketplace

## Overview

Tawssil Pro is a production-ready logistics marketplace web application that connects drivers traveling between cities with people who need to send goods. The platform supports three user roles: drivers who offer transportation services, senders who need to ship items, and administrators who manage the platform. Built with native PHP 8.x backend, vanilla JavaScript frontend, and MySQL database, the application provides comprehensive features including trip/shipment management, real-time matching, messaging system, and multi-language support with RTL for Arabic.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Backend Architecture
- **Framework**: Native PHP 8.x with MVC-inspired structure
- **Routing**: Apache URL rewriting via .htaccess with front controller pattern
- **Session Management**: PHP native sessions for authentication and state management
- **Security**: CSRF protection, password hashing with P<PERSON>'s password_hash/password_verify, prepared PDO statements
- **File Structure**: Organized with controllers, models, views, and utilities separated into logical directories

### Frontend Architecture
- **Styling**: TailwindCSS via CDN for responsive, mobile-first design
- **JavaScript**: Vanilla ES6+ with modular class-based architecture
- **AJAX**: Fetch API for asynchronous server communication
- **Real-time Features**: Polling-based notifications (20-second intervals)
- **Internationalization**: Multi-language support (Arabic RTL, French, English) with PHP-based translation system

### Data Storage
- **Database**: MySQL 8.x with normalized schema design
- **ORM**: Native PDO with prepared statements for SQL injection prevention
- **File Storage**: Local filesystem for user uploads (photos, avatments) with MIME type validation

### Authentication & Authorization
- **Authentication**: Session-based with secure password hashing
- **Authorization**: Role-based access control (driver, sender, admin)
- **Security**: CSRF tokens on all forms, server-side validation, file upload hardening

### Core Domain Models
- **Users**: Multi-role system with profile management
- **Trips**: Driver-created transportation offerings with capacity and pricing
- **Shipments**: Sender-created shipping requests with item details
- **Offers/Bookings**: Bidirectional matching system between trips and shipments
- **Messages**: 1-to-1 communication per offer/transaction
- **Notifications**: In-app notification system with unread tracking

### Business Logic
- **Matching Algorithm**: Route-based search with relevance scoring (exact route matches prioritized, nearby dates secondary)
- **Booking Flow**: Request-based system with pending/accepted/rejected/cancelled statuses
- **Communication**: Integrated messaging tied to specific transactions
- **Administrative Controls**: User management, content moderation, and platform oversight

## External Dependencies

### Frontend Dependencies
- **TailwindCSS**: CSS framework via CDN for responsive design and component styling
- **Browser APIs**: Geolocation API for location tracking, Fetch API for AJAX requests

### Backend Dependencies
- **PHP 8.x**: Core runtime with built-in session management and PDO
- **MySQL 8.x**: Relational database for persistent data storage
- **Apache Web Server**: HTTP server with mod_rewrite for clean URLs

### Development Tools
- **Apache .htaccess**: URL rewriting configuration for single-point entry routing
- **File System**: Local storage for user-uploaded assets with security validation

### Third-party Integrations
- **Map Services**: Placeholder integration for location visualization and route planning
- **Translation System**: PHP array-based internationalization with dynamic language switching
- **Notification System**: Polling-based real-time updates without external messaging services