<?php 
$title = $translations['create_trip'] ?? 'Create Trip - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';
?>

<div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900"><?= $translations['create_trip'] ?? 'Create New Trip' ?></h1>
        <p class="text-gray-600 mt-2"><?= $translations['create_trip_subtitle'] ?? 'Post your available transportation route to connect with senders' ?></p>
    </div>

    <!-- Form -->
    <div class="bg-white shadow rounded-lg">
        <form class="ajax-form" action="/trips/create" method="POST">
            <div class="px-6 py-6 space-y-6">
                <!-- Route Information -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['route_information'] ?? 'Route Information' ?></h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="from_city" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['from_city'] ?? 'From City' ?> *
                            </label>
                            <input type="text" id="from_city" name="from_city" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="<?= $translations['enter_departure_city'] ?? 'Enter departure city' ?>"
                                   value="<?= htmlspecialchars($data['from_city'] ?? '') ?>">
                        </div>
                        
                        <div>
                            <label for="to_city" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['to_city'] ?? 'To City' ?> *
                            </label>
                            <input type="text" id="to_city" name="to_city" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="<?= $translations['enter_destination_city'] ?? 'Enter destination city' ?>"
                                   value="<?= htmlspecialchars($data['to_city'] ?? '') ?>">
                        </div>
                    </div>
                </div>

                <!-- Trip Details -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['trip_details'] ?? 'Trip Details' ?></h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="trip_date" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['trip_date'] ?? 'Trip Date' ?> *
                            </label>
                            <input type="date" id="trip_date" name="trip_date" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   min="<?= date('Y-m-d') ?>"
                                   value="<?= htmlspecialchars($data['trip_date'] ?? '') ?>">
                        </div>
                        
                        <div>
                            <label for="capacity_kg" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['capacity_kg'] ?? 'Capacity (kg)' ?> *
                            </label>
                            <input type="number" id="capacity_kg" name="capacity_kg" required min="1" step="0.1"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="<?= $translations['enter_capacity'] ?? 'e.g., 500' ?>"
                                   value="<?= htmlspecialchars($data['capacity_kg'] ?? '') ?>">
                        </div>
                        
                        <div>
                            <label for="price_per_kg" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['price_per_kg'] ?? 'Price per kg (MAD)' ?> *
                            </label>
                            <input type="number" id="price_per_kg" name="price_per_kg" required min="0.1" step="0.1"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="<?= $translations['enter_price'] ?? 'e.g., 2.5' ?>"
                                   value="<?= htmlspecialchars($data['price_per_kg'] ?? '') ?>">
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                        <?= $translations['notes'] ?? 'Notes' ?>
                    </label>
                    <textarea id="notes" name="notes" rows="4"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="<?= $translations['trip_notes_placeholder'] ?? 'Add any special instructions, vehicle type, or additional information...' ?>"><?= htmlspecialchars($data['notes'] ?? '') ?></textarea>
                </div>

                <!-- Terms and Guidelines -->
                <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <h4 class="text-sm font-medium text-blue-800 mb-2"><?= $translations['important_guidelines'] ?? 'Important Guidelines' ?></h4>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li>• <?= $translations['guideline_1'] ?? 'Ensure your vehicle has proper insurance and registration' ?></li>
                        <li>• <?= $translations['guideline_2'] ?? 'Be responsive to messages and offers from senders' ?></li>
                        <li>• <?= $translations['guideline_3'] ?? 'Confirm pickup and delivery details before accepting offers' ?></li>
                        <li>• <?= $translations['guideline_4'] ?? 'Handle all goods with care and professionalism' ?></li>
                    </ul>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between items-center">
                <a href="/trips" class="text-gray-600 hover:text-gray-800 font-medium">
                    <i class="fas fa-arrow-<?= $isRTL ? 'right' : 'left' ?> <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                    <?= $translations['back_to_trips'] ?? 'Back to Trips' ?>
                </a>
                
                <div class="flex space-x-3 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                    <button type="button" onclick="window.history.back()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium">
                        <?= $translations['cancel'] ?? 'Cancel' ?>
                    </button>
                    <button type="submit" 
                            data-original-text="<?= $translations['create_trip'] ?? 'Create Trip' ?>"
                            class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium transition duration-300">
                        <i class="fas fa-plus <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                        <?= $translations['create_trip'] ?? 'Create Trip' ?>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Quick Tips -->
    <div class="mt-8 bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['tips_for_success'] ?? 'Tips for Success' ?></h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="flex items-start">
                <i class="fas fa-lightbulb text-yellow-500 <?= $isRTL ? 'ml-3' : 'mr-3' ?> mt-1"></i>
                <div>
                    <h4 class="font-medium text-gray-900"><?= $translations['competitive_pricing'] ?? 'Set Competitive Pricing' ?></h4>
                    <p class="text-sm text-gray-600"><?= $translations['pricing_tip'] ?? 'Research similar routes and set fair prices to attract more offers.' ?></p>
                </div>
            </div>
            
            <div class="flex items-start">
                <i class="fas fa-clock text-blue-500 <?= $isRTL ? 'ml-3' : 'mr-3' ?> mt-1"></i>
                <div>
                    <h4 class="font-medium text-gray-900"><?= $translations['flexible_timing'] ?? 'Be Flexible' ?></h4>
                    <p class="text-sm text-gray-600"><?= $translations['timing_tip'] ?? 'Consider flexible pickup times to accommodate more senders.' ?></p>
                </div>
            </div>
            
            <div class="flex items-start">
                <i class="fas fa-shield-alt text-green-500 <?= $isRTL ? 'ml-3' : 'mr-3' ?> mt-1"></i>
                <div>
                    <h4 class="font-medium text-gray-900"><?= $translations['safety_first'] ?? 'Safety First' ?></h4>
                    <p class="text-sm text-gray-600"><?= $translations['safety_tip'] ?? 'Always verify goods and follow safety protocols during transport.' ?></p>
                </div>
            </div>
            
            <div class="flex items-start">
                <i class="fas fa-star text-purple-500 <?= $isRTL ? 'ml-3' : 'mr-3' ?> mt-1"></i>
                <div>
                    <h4 class="font-medium text-gray-900"><?= $translations['build_reputation'] ?? 'Build Your Reputation' ?></h4>
                    <p class="text-sm text-gray-600"><?= $translations['reputation_tip'] ?? 'Complete trips successfully to build trust and get more business.' ?></p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../layout/footer.php'; ?>
