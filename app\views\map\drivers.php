<?php 
$title = $translations['driver_map'] ?? 'Driver Map - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900"><?= $translations['driver_map'] ?? 'Driver Map' ?></h1>
        <p class="text-gray-600 mt-2"><?= $translations['find_drivers_nearby'] ?? 'Find drivers in your area and track their real-time locations' ?></p>
    </div>

    <!-- Controls -->
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="px-6 py-4">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <!-- Filters -->
                <div class="flex flex-wrap items-center gap-4">
                    <div>
                        <label for="cityFilter" class="block text-sm font-medium text-gray-700 mb-1">
                            <?= $translations['filter_by_city'] ?? 'Filter by City' ?>
                        </label>
                        <select id="cityFilter" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value=""><?= $translations['all_cities'] ?? 'All Cities' ?></option>
                            <option value="Casablanca"><?= $translations['casablanca'] ?? 'Casablanca' ?></option>
                            <option value="Rabat"><?= $translations['rabat'] ?? 'Rabat' ?></option>
                            <option value="Marrakech"><?= $translations['marrakech'] ?? 'Marrakech' ?></option>
                            <option value="Fes"><?= $translations['fes'] ?? 'Fes' ?></option>
                            <option value="Tangier"><?= $translations['tangier'] ?? 'Tangier' ?></option>
                            <option value="Agadir"><?= $translations['agadir'] ?? 'Agadir' ?></option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="vehicleFilter" class="block text-sm font-medium text-gray-700 mb-1">
                            <?= $translations['vehicle_type'] ?? 'Vehicle Type' ?>
                        </label>
                        <select id="vehicleFilter" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value=""><?= $translations['all_vehicles'] ?? 'All Vehicles' ?></option>
                            <option value="truck"><?= $translations['truck'] ?? 'Truck' ?></option>
                            <option value="van"><?= $translations['van'] ?? 'Van' ?></option>
                            <option value="pickup"><?= $translations['pickup_truck'] ?? 'Pickup Truck' ?></option>
                            <option value="car"><?= $translations['car'] ?? 'Car' ?></option>
                            <option value="motorcycle"><?= $translations['motorcycle'] ?? 'Motorcycle' ?></option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">&nbsp;</label>
                        <button id="refreshMap" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm font-medium transition duration-300">
                            <i class="fas fa-sync-alt <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                            <?= $translations['refresh'] ?? 'Refresh' ?>
                        </button>
                    </div>
                </div>
                
                <!-- Map Controls -->
                <div class="flex items-center space-x-3 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                    <button id="centerMap" class="px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-sm">
                        <i class="fas fa-crosshairs <?= $isRTL ? 'ml-1' : 'mr-1' ?>"></i>
                        <?= $translations['center_map'] ?? 'Center Map' ?>
                    </button>
                    
                    <button id="toggleFullscreen" class="px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-sm">
                        <i class="fas fa-expand <?= $isRTL ? 'ml-1' : 'mr-1' ?>"></i>
                        <?= $translations['fullscreen'] ?? 'Fullscreen' ?>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Map Container -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="relative">
            <div id="map" class="w-full h-96 md:h-[600px]"></div>
            
            <!-- Loading Overlay -->
            <div id="mapLoading" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin text-3xl text-blue-600 mb-2"></i>
                    <p class="text-gray-600"><?= $translations['loading_drivers'] ?? 'Loading drivers...' ?></p>
                </div>
            </div>
            
            <!-- Driver Count -->
            <div class="absolute top-4 <?= $isRTL ? 'left-4' : 'right-4' ?> bg-white rounded-lg shadow-lg px-4 py-2">
                <div class="flex items-center space-x-2 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                    <i class="fas fa-truck text-blue-600"></i>
                    <span class="text-sm font-medium text-gray-900">
                        <span id="driverCount">0</span> <?= $translations['drivers_online'] ?? 'drivers online' ?>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Driver List -->
    <div class="mt-8">
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900"><?= $translations['nearby_drivers'] ?? 'Nearby Drivers' ?></h2>
            </div>
            
            <div id="driversList" class="divide-y divide-gray-200">
                <!-- Driver cards will be inserted here -->
            </div>
        </div>
    </div>

    <!-- Legend -->
    <div class="mt-6 bg-gray-50 rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['map_legend'] ?? 'Map Legend' ?></h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="flex items-center">
                <div class="w-6 h-6 bg-blue-500 rounded-full border-2 border-white shadow-lg <?= $isRTL ? 'ml-3' : 'mr-3' ?>"></div>
                <span class="text-sm text-gray-700"><?= $translations['available_driver'] ?? 'Available Driver' ?></span>
            </div>
            <div class="flex items-center">
                <div class="w-6 h-6 bg-green-500 rounded-full border-2 border-white shadow-lg <?= $isRTL ? 'ml-3' : 'mr-3' ?>"></div>
                <span class="text-sm text-gray-700"><?= $translations['active_trip'] ?? 'On Trip' ?></span>
            </div>
            <div class="flex items-center">
                <div class="w-6 h-6 bg-yellow-500 rounded-full border-2 border-white shadow-lg <?= $isRTL ? 'ml-3' : 'mr-3' ?>"></div>
                <span class="text-sm text-gray-700"><?= $translations['busy_driver'] ?? 'Busy' ?></span>
            </div>
            <div class="flex items-center">
                <div class="w-6 h-6 bg-gray-400 rounded-full border-2 border-white shadow-lg <?= $isRTL ? 'ml-3' : 'mr-3' ?>"></div>
                <span class="text-sm text-gray-700"><?= $translations['offline_driver'] ?? 'Offline' ?></span>
            </div>
        </div>
    </div>
</div>

<!-- Driver Details Modal -->
<div id="driverModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900"><?= $translations['driver_details'] ?? 'Driver Details' ?></h3>
                <button onclick="closeDriverModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div id="driverModalContent">
                <!-- Driver details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
class DriverMap {
    constructor() {
        this.map = null;
        this.driverMarkers = [];
        this.driverData = [];
        this.isFullscreen = false;
        this.init();
    }

    init() {
        this.initMap();
        this.loadDrivers();
        this.setupEventListeners();
        this.startAutoRefresh();
    }

    initMap() {
        // Initialize map centered on Morocco
        this.map = L.map('map').setView([33.5731, -7.5898], 6);

        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors',
            maxZoom: 18
        }).addTo(this.map);

        // Add scale control
        L.control.scale().addTo(this.map);

        // Hide loading overlay once map is ready
        this.map.whenReady(() => {
            document.getElementById('mapLoading').style.display = 'none';
        });
    }

    setupEventListeners() {
        // Refresh button
        document.getElementById('refreshMap').addEventListener('click', () => {
            this.loadDrivers();
        });

        // Center map button
        document.getElementById('centerMap').addEventListener('click', () => {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition((position) => {
                    const { latitude, longitude } = position.coords;
                    this.map.setView([latitude, longitude], 12);
                }, () => {
                    // Fallback to Morocco center
                    this.map.setView([33.5731, -7.5898], 6);
                });
            } else {
                this.map.setView([33.5731, -7.5898], 6);
            }
        });

        // Fullscreen toggle
        document.getElementById('toggleFullscreen').addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // Filters
        document.getElementById('cityFilter').addEventListener('change', () => {
            this.filterDrivers();
        });

        document.getElementById('vehicleFilter').addEventListener('change', () => {
            this.filterDrivers();
        });
    }

    async loadDrivers() {
        try {
            const response = await fetch('/api/drivers/locations');
            this.driverData = await response.json();
            this.updateMap();
            this.updateDriversList();
            this.updateDriverCount();
        } catch (error) {
            console.error('Error loading drivers:', error);
            window.tawssil.showToast('<?= $translations["error_loading_drivers"] ?? "Error loading drivers" ?>', 'error');
        }
    }

    updateMap() {
        // Clear existing markers
        this.driverMarkers.forEach(marker => this.map.removeLayer(marker));
        this.driverMarkers = [];

        // Add new markers
        this.driverData.forEach(driver => {
            if (driver.latitude && driver.longitude) {
                const marker = this.createDriverMarker(driver);
                this.driverMarkers.push(marker);
            }
        });
    }

    createDriverMarker(driver) {
        // Determine marker color based on driver status
        let color = '#3B82F6'; // blue - available
        if (driver.status === 'on_trip') color = '#10B981'; // green
        else if (driver.status === 'busy') color = '#F59E0B'; // yellow
        else if (driver.status === 'offline') color = '#6B7280'; // gray

        // Create custom icon
        const icon = L.divIcon({
            html: `<div class="driver-marker" style="background-color: ${color}; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3); display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-truck text-white text-xs"></i>
                   </div>`,
            className: 'custom-driver-icon',
            iconSize: [24, 24],
            iconAnchor: [12, 12]
        });

        const marker = L.marker([driver.latitude, driver.longitude], { icon })
            .addTo(this.map);

        // Create popup content
        const popupContent = `
            <div class="p-2 min-w-48">
                <h3 class="font-semibold text-gray-900">${driver.name}</h3>
                <p class="text-sm text-gray-600">${driver.vehicle_type || '<?= $translations["vehicle_type_unknown"] ?? "Unknown vehicle" ?>'}</p>
                <p class="text-xs text-gray-500 mt-1">
                    <?= $translations["last_updated"] ?? "Last updated" ?>: ${new Date(driver.updated_at).toLocaleTimeString()}
                </p>
                <div class="mt-2">
                    <button onclick="showDriverDetails(${driver.id})" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        <?= $translations["view_details"] ?? "View Details" ?>
                    </button>
                </div>
            </div>
        `;

        marker.bindPopup(popupContent);

        // Store driver data on marker for filtering
        marker.driverData = driver;

        return marker;
    }

    updateDriversList() {
        const container = document.getElementById('driversList');
        
        if (this.driverData.length === 0) {
            container.innerHTML = `
                <div class="px-6 py-8 text-center">
                    <i class="fas fa-truck text-4xl text-gray-300 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2"><?= $translations["no_drivers_online"] ?? "No drivers online" ?></h3>
                    <p class="text-gray-600"><?= $translations["no_drivers_description"] ?? "There are no drivers currently sharing their location." ?></p>
                </div>
            `;
            return;
        }

        const driversHtml = this.driverData.map(driver => `
            <div class="px-6 py-4 hover:bg-gray-50 cursor-pointer" onclick="focusDriver(${driver.id})">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 rounded-full ${this.getStatusColor(driver.status)} <?= $isRTL ? 'ml-3' : 'mr-3' ?>"></div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-900">${driver.name}</h3>
                            <p class="text-sm text-gray-500">${driver.vehicle_type || '<?= $translations["vehicle_type_unknown"] ?? "Unknown vehicle" ?>'}</p>
                        </div>
                    </div>
                    <div class="text-<?= $isRTL ? 'left' : 'right' ?>">
                        <span class="text-xs text-gray-500">
                            ${new Date(driver.updated_at).toLocaleTimeString()}
                        </span>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = driversHtml;
    }

    getStatusColor(status) {
        switch (status) {
            case 'on_trip': return 'bg-green-500';
            case 'busy': return 'bg-yellow-500';
            case 'offline': return 'bg-gray-400';
            default: return 'bg-blue-500';
        }
    }

    updateDriverCount() {
        document.getElementById('driverCount').textContent = this.driverData.length;
    }

    filterDrivers() {
        const cityFilter = document.getElementById('cityFilter').value;
        const vehicleFilter = document.getElementById('vehicleFilter').value;

        this.driverMarkers.forEach(marker => {
            const driver = marker.driverData;
            let show = true;

            if (cityFilter && !driver.current_city?.includes(cityFilter)) {
                show = false;
            }

            if (vehicleFilter && driver.vehicle_type !== vehicleFilter) {
                show = false;
            }

            if (show) {
                marker.addTo(this.map);
            } else {
                this.map.removeLayer(marker);
            }
        });
    }

    toggleFullscreen() {
        const mapContainer = document.getElementById('map');
        
        if (!this.isFullscreen) {
            mapContainer.classList.add('fixed', 'inset-0', 'z-50', 'h-screen');
            document.getElementById('toggleFullscreen').innerHTML = '<i class="fas fa-compress <?= $isRTL ? "ml-1" : "mr-1" ?>"></i><?= $translations["exit_fullscreen"] ?? "Exit Fullscreen" ?>';
            this.isFullscreen = true;
        } else {
            mapContainer.classList.remove('fixed', 'inset-0', 'z-50', 'h-screen');
            document.getElementById('toggleFullscreen').innerHTML = '<i class="fas fa-expand <?= $isRTL ? "ml-1" : "mr-1" ?>"></i><?= $translations["fullscreen"] ?? "Fullscreen" ?>';
            this.isFullscreen = false;
        }

        // Invalidate map size after DOM changes
        setTimeout(() => {
            this.map.invalidateSize();
        }, 100);
    }

    startAutoRefresh() {
        // Refresh driver locations every 30 seconds
        setInterval(() => {
            this.loadDrivers();
        }, 30000);
    }
}

// Global functions
let driverMap;

function focusDriver(driverId) {
    const driver = driverMap.driverData.find(d => d.id === driverId);
    if (driver && driver.latitude && driver.longitude) {
        driverMap.map.setView([driver.latitude, driver.longitude], 15);
        
        // Find and open the marker popup
        const marker = driverMap.driverMarkers.find(m => m.driverData.id === driverId);
        if (marker) {
            marker.openPopup();
        }
    }
}

function showDriverDetails(driverId) {
    const driver = driverMap.driverData.find(d => d.id === driverId);
    if (!driver) return;

    const modalContent = document.getElementById('driverModalContent');
    modalContent.innerHTML = `
        <div class="space-y-4">
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-user text-blue-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900">${driver.name}</h3>
            </div>
            
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-500"><?= $translations["vehicle_type"] ?? "Vehicle Type" ?>:</span>
                    <span class="font-medium">${driver.vehicle_type || '<?= $translations["not_specified"] ?? "Not specified" ?>'}</span>
                </div>
                
                <div class="flex justify-between">
                    <span class="text-gray-500"><?= $translations["last_seen"] ?? "Last Seen" ?>:</span>
                    <span class="font-medium">${new Date(driver.updated_at).toLocaleString()}</span>
                </div>
                
                <div class="flex justify-between">
                    <span class="text-gray-500"><?= $translations["location"] ?? "Location" ?>:</span>
                    <span class="font-medium">${driver.latitude.toFixed(4)}, ${driver.longitude.toFixed(4)}</span>
                </div>
            </div>
            
            <div class="mt-6 flex space-x-3 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                <button onclick="focusDriver(${driver.id}); closeDriverModal();" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                    <?= $translations["view_on_map"] ?? "View on Map" ?>
                </button>
            </div>
        </div>
    `;

    document.getElementById('driverModal').classList.remove('hidden');
}

function closeDriverModal() {
    document.getElementById('driverModal').classList.add('hidden');
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    driverMap = new DriverMap();
});

// Close modal when clicking outside
document.getElementById('driverModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDriverModal();
    }
});
</script>

<?php include __DIR__ . '/../layout/footer.php'; ?>
