<?php 
$title = $translations['edit_trip'] ?? 'Edit Trip - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';
?>

<div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900"><?= $translations['edit_trip'] ?? 'Edit Trip' ?></h1>
        <p class="text-gray-600 mt-2"><?= $translations['edit_trip_subtitle'] ?? 'Update your trip information' ?></p>
    </div>

    <!-- Form -->
    <div class="bg-white shadow rounded-lg">
        <form class="ajax-form" action="/trips/<?= $trip['id'] ?>/edit" method="POST">
            <div class="px-6 py-6 space-y-6">
                <!-- Route Information -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['route_information'] ?? 'Route Information' ?></h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="from_city" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['from_city'] ?? 'From City' ?> *
                            </label>
                            <input type="text" id="from_city" name="from_city" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="<?= $translations['enter_departure_city'] ?? 'Enter departure city' ?>"
                                   value="<?= htmlspecialchars($trip['from_city']) ?>">
                        </div>
                        
                        <div>
                            <label for="to_city" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['to_city'] ?? 'To City' ?> *
                            </label>
                            <input type="text" id="to_city" name="to_city" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="<?= $translations['enter_destination_city'] ?? 'Enter destination city' ?>"
                                   value="<?= htmlspecialchars($trip['to_city']) ?>">
                        </div>
                    </div>
                </div>

                <!-- Trip Details -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['trip_details'] ?? 'Trip Details' ?></h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="trip_date" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['trip_date'] ?? 'Trip Date' ?> *
                            </label>
                            <input type="date" id="trip_date" name="trip_date" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   min="<?= date('Y-m-d') ?>"
                                   value="<?= htmlspecialchars($trip['trip_date']) ?>">
                        </div>
                        
                        <div>
                            <label for="capacity_kg" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['capacity_kg'] ?? 'Capacity (kg)' ?> *
                            </label>
                            <input type="number" id="capacity_kg" name="capacity_kg" required min="1" step="0.1"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="<?= $translations['enter_capacity'] ?? 'e.g., 500' ?>"
                                   value="<?= htmlspecialchars($trip['capacity_kg']) ?>">
                        </div>
                        
                        <div>
                            <label for="price_per_kg" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['price_per_kg'] ?? 'Price per kg (MAD)' ?> *
                            </label>
                            <input type="number" id="price_per_kg" name="price_per_kg" required min="0.1" step="0.1"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="<?= $translations['enter_price'] ?? 'e.g., 2.5' ?>"
                                   value="<?= htmlspecialchars($trip['price_per_kg']) ?>">
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                        <?= $translations['notes'] ?? 'Notes' ?>
                    </label>
                    <textarea id="notes" name="notes" rows="4"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="<?= $translations['trip_notes_placeholder'] ?? 'Add any special instructions, vehicle type, or additional information...' ?>"><?= htmlspecialchars($trip['notes']) ?></textarea>
                </div>

                <!-- Trip Status -->
                <div class="bg-gray-50 border border-gray-200 rounded-md p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900"><?= $translations['trip_status'] ?? 'Trip Status' ?></h4>
                            <p class="text-sm text-gray-600"><?= $translations['current_status'] ?? 'Current status' ?>: 
                                <span class="font-medium capitalize <?= 
                                    $trip['status'] === 'open' ? 'text-green-600' : 
                                    ($trip['status'] === 'full' ? 'text-blue-600' : 'text-gray-600')
                                ?>">
                                    <?= $translations['status_' . $trip['status']] ?? ucfirst($trip['status']) ?>
                                </span>
                            </p>
                        </div>
                        <span class="px-3 py-1 text-sm font-medium rounded-full <?= 
                            $trip['status'] === 'open' ? 'bg-green-100 text-green-800' : 
                            ($trip['status'] === 'full' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800')
                        ?>">
                            <?= $translations['status_' . $trip['status']] ?? ucfirst($trip['status']) ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between items-center">
                <a href="/trips/<?= $trip['id'] ?>" class="text-gray-600 hover:text-gray-800 font-medium">
                    <i class="fas fa-arrow-<?= $isRTL ? 'right' : 'left' ?> <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                    <?= $translations['back_to_trip'] ?? 'Back to Trip' ?>
                </a>
                
                <div class="flex space-x-3 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                    <button type="button" onclick="window.history.back()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium">
                        <?= $translations['cancel'] ?? 'Cancel' ?>
                    </button>
                    <button type="submit" 
                            data-original-text="<?= $translations['update_trip'] ?? 'Update Trip' ?>"
                            class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium transition duration-300">
                        <i class="fas fa-save <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                        <?= $translations['update_trip'] ?? 'Update Trip' ?>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Warning Notice -->
    <?php if ($trip['status'] !== 'open'): ?>
        <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div class="flex">
                <i class="fas fa-exclamation-triangle text-yellow-400 <?= $isRTL ? 'ml-3' : 'mr-3' ?>"></i>
                <div>
                    <h3 class="text-sm font-medium text-yellow-800"><?= $translations['notice'] ?? 'Notice' ?></h3>
                    <p class="mt-1 text-sm text-yellow-700">
                        <?= $translations['trip_not_open_notice'] ?? 'This trip is not currently open for new offers. Changes may not affect existing agreements.' ?>
                    </p>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php include __DIR__ . '/../layout/footer.php'; ?>
