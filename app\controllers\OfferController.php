<?php
class OfferController extends Controller {
    
    public function index() {
        $this->requireAuth();
        
        $userId = $this->auth->id();
        
        // Get offers where user is involved (either as proposer or receiver)
        $stmt = $this->db->query(
            "SELECT o.*, 
                    CASE 
                        WHEN o.trip_id IS NOT NULL THEN 'trip_offer'
                        ELSE 'shipment_offer'
                    END as offer_type,
                    CASE 
                        WHEN o.trip_id IS NOT NULL THEN CONCAT(t.from_city, ' → ', t.to_city)
                        ELSE CONCAT(s.from_city, ' → ', s.to_city)
                    END as route,
                    CASE 
                        WHEN o.trip_id IS NOT NULL THEN t.trip_date
                        ELSE s.ready_date
                    END as date,
                    proposer.name as proposer_name,
                    receiver.name as receiver_name
             FROM offers o
             LEFT JOIN trips t ON o.trip_id = t.id
             LEFT JOIN shipments s ON o.shipment_id = s.id
             JOIN users proposer ON o.proposer_id = proposer.id
             JOIN users receiver ON o.receiver_id = receiver.id
             WHERE o.proposer_id = ? OR o.receiver_id = ?
             ORDER BY o.created_at DESC",
            [$userId, $userId]
        );
        
        $offers = $stmt->fetchAll();
        
        $this->view('offers/index', compact('offers'));
    }
    
    public function show($id) {
        $this->requireAuth();
        
        $userId = $this->auth->id();
        
        $stmt = $this->db->query(
            "SELECT o.*, 
                    t.from_city as trip_from, t.to_city as trip_to, t.trip_date, t.capacity_kg, t.price_per_kg,
                    s.from_city as shipment_from, s.to_city as shipment_to, s.ready_date, s.weight_kg, s.description, s.fragile,
                    proposer.name as proposer_name, proposer.phone as proposer_phone, proposer.email as proposer_email,
                    receiver.name as receiver_name, receiver.phone as receiver_phone, receiver.email as receiver_email
             FROM offers o
             LEFT JOIN trips t ON o.trip_id = t.id
             LEFT JOIN shipments s ON o.shipment_id = s.id
             JOIN users proposer ON o.proposer_id = proposer.id
             JOIN users receiver ON o.receiver_id = receiver.id
             WHERE o.id = ? AND (o.proposer_id = ? OR o.receiver_id = ?)",
            [$id, $userId, $userId]
        );
        
        $offer = $stmt->fetch();
        
        if (!$offer) {
            $this->redirect('/offers');
        }
        
        // Get messages for this offer
        $messagesStmt = $this->db->query(
            "SELECT m.*, u.name as sender_name
             FROM messages m
             JOIN users u ON m.sender_id = u.id
             WHERE m.offer_id = ?
             ORDER BY m.created_at ASC",
            [$id]
        );
        
        $messages = $messagesStmt->fetchAll();
        
        $this->view('offers/show', compact('offer', 'messages'));
    }
    
    public function store() {
        $this->requireAuth();
        $this->validateCsrf();
        
        $tripId = $_POST['trip_id'] ?? null;
        $shipmentId = $_POST['shipment_id'] ?? null;
        $priceTotal = $_POST['price_total'] ?? null;
        
        // Validation
        if (!$tripId && !$shipmentId) {
            $this->json(['success' => false, 'message' => 'Either trip or shipment must be specified']);
        }
        
        if ($tripId && $shipmentId) {
            $this->json(['success' => false, 'message' => 'Cannot specify both trip and shipment']);
        }
        
        $userId = $this->auth->id();
        $userRole = $_SESSION['user']['role'];
        
        if ($tripId) {
            // Sender making an offer to a driver's trip
            if ($userRole !== 'sender') {
                $this->json(['success' => false, 'message' => 'Only senders can make offers to trips']);
            }
            
            // Get trip details
            $stmt = $this->db->query("SELECT * FROM trips WHERE id = ? AND status = 'open'", [$tripId]);
            $trip = $stmt->fetch();
            
            if (!$trip) {
                $this->json(['success' => false, 'message' => 'Trip not found or not available']);
            }
            
            $receiverId = $trip['user_id'];
            
            // Check if offer already exists
            $existingStmt = $this->db->query(
                "SELECT id FROM offers WHERE trip_id = ? AND proposer_id = ? AND status != 'cancelled'",
                [$tripId, $userId]
            );
            
            if ($existingStmt->fetch()) {
                $this->json(['success' => false, 'message' => 'You have already made an offer for this trip']);
            }
            
        } else {
            // Driver making an offer to a sender's shipment
            if ($userRole !== 'driver') {
                $this->json(['success' => false, 'message' => 'Only drivers can make offers to shipments']);
            }
            
            // Get shipment details
            $stmt = $this->db->query("SELECT * FROM shipments WHERE id = ? AND status = 'open'", [$shipmentId]);
            $shipment = $stmt->fetch();
            
            if (!$shipment) {
                $this->json(['success' => false, 'message' => 'Shipment not found or not available']);
            }
            
            $receiverId = $shipment['user_id'];
            
            // Check if offer already exists
            $existingStmt = $this->db->query(
                "SELECT id FROM offers WHERE shipment_id = ? AND proposer_id = ? AND status != 'cancelled'",
                [$shipmentId, $userId]
            );
            
            if ($existingStmt->fetch()) {
                $this->json(['success' => false, 'message' => 'You have already made an offer for this shipment']);
            }
        }
        
        // Cannot make offer to yourself
        if ($receiverId == $userId) {
            $this->json(['success' => false, 'message' => 'Cannot make offer to yourself']);
        }
        
        // Insert offer
        $stmt = $this->db->query(
            "INSERT INTO offers (trip_id, shipment_id, proposer_id, receiver_id, price_total, created_at, updated_at) 
             VALUES (?, ?, ?, ?, ?, NOW(), NOW())",
            [$tripId, $shipmentId, $userId, $receiverId, $priceTotal]
        );
        
        if ($stmt) {
            $offerId = $this->db->lastInsertId();
            
            // Create notification
            $notificationModel = new Notification();
            $notificationModel->create([
                'user_id' => $receiverId,
                'type' => 'new_offer',
                'ref_id' => $offerId
            ]);
            
            $this->json(['success' => true, 'message' => 'Offer sent successfully', 'offer_id' => $offerId]);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to send offer']);
        }
    }
    
    public function accept($id) {
        $this->requireAuth();
        $this->validateCsrf();
        
        $userId = $this->auth->id();
        
        // Get offer details
        $stmt = $this->db->query(
            "SELECT * FROM offers WHERE id = ? AND receiver_id = ? AND status = 'pending'",
            [$id, $userId]
        );
        
        $offer = $stmt->fetch();
        
        if (!$offer) {
            $this->json(['success' => false, 'message' => 'Offer not found or cannot be accepted']);
        }
        
        $this->db->beginTransaction();
        
        try {
            // Update offer status
            $this->db->query(
                "UPDATE offers SET status = 'accepted', updated_at = NOW() WHERE id = ?",
                [$id]
            );
            
            // Update trip/shipment status
            if ($offer['trip_id']) {
                $this->db->query(
                    "UPDATE trips SET status = 'full', updated_at = NOW() WHERE id = ?",
                    [$offer['trip_id']]
                );
            } else {
                $this->db->query(
                    "UPDATE shipments SET status = 'matched', updated_at = NOW() WHERE id = ?",
                    [$offer['shipment_id']]
                );
            }
            
            // Create notification
            $notificationModel = new Notification();
            $notificationModel->create([
                'user_id' => $offer['proposer_id'],
                'type' => 'offer_accepted',
                'ref_id' => $id
            ]);
            
            $this->db->commit();
            
            $this->json(['success' => true, 'message' => 'Offer accepted successfully']);
            
        } catch (Exception $e) {
            $this->db->rollback();
            $this->json(['success' => false, 'message' => 'Failed to accept offer']);
        }
    }
    
    public function reject($id) {
        $this->requireAuth();
        $this->validateCsrf();
        
        $userId = $this->auth->id();
        
        // Get offer details
        $stmt = $this->db->query(
            "SELECT * FROM offers WHERE id = ? AND receiver_id = ? AND status = 'pending'",
            [$id, $userId]
        );
        
        $offer = $stmt->fetch();
        
        if (!$offer) {
            $this->json(['success' => false, 'message' => 'Offer not found or cannot be rejected']);
        }
        
        // Update offer status
        $stmt = $this->db->query(
            "UPDATE offers SET status = 'rejected', updated_at = NOW() WHERE id = ?",
            [$id]
        );
        
        if ($stmt) {
            // Create notification
            $notificationModel = new Notification();
            $notificationModel->create([
                'user_id' => $offer['proposer_id'],
                'type' => 'offer_rejected',
                'ref_id' => $id
            ]);
            
            $this->json(['success' => true, 'message' => 'Offer rejected']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to reject offer']);
        }
    }
    
    public function cancel($id) {
        $this->requireAuth();
        $this->validateCsrf();
        
        $userId = $this->auth->id();
        
        // Get offer details
        $stmt = $this->db->query(
            "SELECT * FROM offers WHERE id = ? AND proposer_id = ? AND status IN ('pending', 'accepted')",
            [$id, $userId]
        );
        
        $offer = $stmt->fetch();
        
        if (!$offer) {
            $this->json(['success' => false, 'message' => 'Offer not found or cannot be cancelled']);
        }
        
        $this->db->beginTransaction();
        
        try {
            // Update offer status
            $this->db->query(
                "UPDATE offers SET status = 'cancelled', updated_at = NOW() WHERE id = ?",
                [$id]
            );
            
            // If offer was accepted, reopen the trip/shipment
            if ($offer['status'] === 'accepted') {
                if ($offer['trip_id']) {
                    $this->db->query(
                        "UPDATE trips SET status = 'open', updated_at = NOW() WHERE id = ?",
                        [$offer['trip_id']]
                    );
                } else {
                    $this->db->query(
                        "UPDATE shipments SET status = 'open', updated_at = NOW() WHERE id = ?",
                        [$offer['shipment_id']]
                    );
                }
            }
            
            // Create notification
            $notificationModel = new Notification();
            $notificationModel->create([
                'user_id' => $offer['receiver_id'],
                'type' => 'offer_cancelled',
                'ref_id' => $id
            ]);
            
            $this->db->commit();
            
            $this->json(['success' => true, 'message' => 'Offer cancelled successfully']);
            
        } catch (Exception $e) {
            $this->db->rollback();
            $this->json(['success' => false, 'message' => 'Failed to cancel offer']);
        }
    }
}
?>
