<?php 
$title = $translations['offers'] ?? 'Offers - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900"><?= $translations['offers'] ?? 'Offers' ?></h1>
        <p class="text-gray-600 mt-2"><?= $translations['manage_your_offers'] ?? 'Manage your offers and negotiations' ?></p>
    </div>

    <!-- Filter Tabs -->
    <div class="mb-8">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                <a href="?status=all" class="<?= ($_GET['status'] ?? 'all') === 'all' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' ?> whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    <?= $translations['all_offers'] ?? 'All Offers' ?>
                    <span class="<?= ($_GET['status'] ?? 'all') === 'all' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-900' ?> hidden ml-2 py-0.5 px-2.5 rounded-full text-xs font-medium md:inline-block">
                        <?= count($offers) ?>
                    </span>
                </a>
                
                <a href="?status=pending" class="<?= ($_GET['status'] ?? '') === 'pending' ? 'border-yellow-500 text-yellow-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' ?> whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    <?= $translations['pending'] ?? 'Pending' ?>
                    <span class="<?= ($_GET['status'] ?? '') === 'pending' ? 'bg-yellow-100 text-yellow-600' : 'bg-gray-100 text-gray-900' ?> hidden ml-2 py-0.5 px-2.5 rounded-full text-xs font-medium md:inline-block">
                        <?= count(array_filter($offers, fn($o) => $o['status'] === 'pending')) ?>
                    </span>
                </a>
                
                <a href="?status=accepted" class="<?= ($_GET['status'] ?? '') === 'accepted' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' ?> whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    <?= $translations['accepted'] ?? 'Accepted' ?>
                    <span class="<?= ($_GET['status'] ?? '') === 'accepted' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-900' ?> hidden ml-2 py-0.5 px-2.5 rounded-full text-xs font-medium md:inline-block">
                        <?= count(array_filter($offers, fn($o) => $o['status'] === 'accepted')) ?>
                    </span>
                </a>
                
                <a href="?status=rejected" class="<?= ($_GET['status'] ?? '') === 'rejected' ? 'border-red-500 text-red-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' ?> whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    <?= $translations['rejected'] ?? 'Rejected' ?>
                    <span class="<?= ($_GET['status'] ?? '') === 'rejected' ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-900' ?> hidden ml-2 py-0.5 px-2.5 rounded-full text-xs font-medium md:inline-block">
                        <?= count(array_filter($offers, fn($o) => $o['status'] === 'rejected')) ?>
                    </span>
                </a>
            </nav>
        </div>
    </div>

    <!-- Offers List -->
    <div class="space-y-6">
        <?php 
        $filteredOffers = $offers;
        if (isset($_GET['status']) && $_GET['status'] !== 'all') {
            $filteredOffers = array_filter($offers, fn($offer) => $offer['status'] === $_GET['status']);
        }
        ?>
        
        <?php if (empty($filteredOffers)): ?>
            <div class="bg-white rounded-lg shadow p-8 text-center">
                <i class="fas fa-handshake text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">
                    <?= $translations['no_offers_found'] ?? 'No offers found' ?>
                </h3>
                <p class="text-gray-600">
                    <?php if (isset($_GET['status']) && $_GET['status'] !== 'all'): ?>
                        <?= $translations['no_offers_status'] ?? 'No offers with this status found.' ?>
                    <?php else: ?>
                        <?= $translations['no_offers_description'] ?? 'You haven\'t made or received any offers yet.' ?>
                    <?php endif; ?>
                </p>
                <div class="mt-4 space-x-4 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                    <?php if ($auth->hasRole('driver')): ?>
                        <a href="/shipments" class="inline-block bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md transition duration-300">
                            <?= $translations['find_shipments'] ?? 'Find Shipments' ?>
                        </a>
                    <?php else: ?>
                        <a href="/trips" class="inline-block bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md transition duration-300">
                            <?= $translations['find_trips'] ?? 'Find Trips' ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($filteredOffers as $offer): ?>
                <div class="bg-white rounded-lg shadow hover:shadow-lg transition duration-300">
                    <div class="p-6">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <!-- Offer Header -->
                                <div class="flex items-center mb-3">
                                    <div class="flex items-center">
                                        <?php if ($offer['offer_type'] === 'trip_offer'): ?>
                                            <i class="fas fa-route text-blue-600 <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                                            <span class="text-sm text-gray-500"><?= $translations['offer_for_trip'] ?? 'Offer for Trip' ?></span>
                                        <?php else: ?>
                                            <i class="fas fa-box text-green-600 <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                                            <span class="text-sm text-gray-500"><?= $translations['offer_for_shipment'] ?? 'Offer for Shipment' ?></span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <span class="<?= $isRTL ? 'mr-3' : 'ml-3' ?> px-2 py-1 text-xs font-medium rounded-full <?= 
                                        $offer['status'] === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                                        ($offer['status'] === 'accepted' ? 'bg-green-100 text-green-800' : 
                                        ($offer['status'] === 'rejected' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'))
                                    ?>">
                                        <?= $translations['status_' . $offer['status']] ?? ucfirst($offer['status']) ?>
                                    </span>
                                </div>
                                
                                <!-- Route Information -->
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                    <?= htmlspecialchars($offer['route']) ?>
                                </h3>
                                
                                <!-- Participants -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <span class="text-sm text-gray-500">
                                            <?php if ($auth->id() == $offer['proposer_id']): ?>
                                                <?= $translations['offered_to'] ?? 'Offered to' ?>:
                                            <?php else: ?>
                                                <?= $translations['offered_by'] ?? 'Offered by' ?>:
                                            <?php endif; ?>
                                        </span>
                                        <p class="font-medium">
                                            <?php if ($auth->id() == $offer['proposer_id']): ?>
                                                <?= htmlspecialchars($offer['receiver_name']) ?>
                                            <?php else: ?>
                                                <?= htmlspecialchars($offer['proposer_name']) ?>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                    
                                    <div>
                                        <span class="text-sm text-gray-500"><?= $translations['date'] ?? 'Date' ?>:</span>
                                        <p class="font-medium"><?= date('M d, Y', strtotime($offer['date'])) ?></p>
                                    </div>
                                </div>
                                
                                <!-- Price -->
                                <?php if ($offer['price_total']): ?>
                                    <div class="mb-4">
                                        <span class="text-sm text-gray-500"><?= $translations['offered_price'] ?? 'Offered Price' ?>:</span>
                                        <p class="text-lg font-bold text-green-600"><?= $offer['price_total'] ?> MAD</p>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Created Date -->
                                <div class="text-sm text-gray-500">
                                    <i class="fas fa-clock <?= $isRTL ? 'ml-1' : 'mr-1' ?>"></i>
                                    <?= $translations['created'] ?? 'Created' ?>: <?= date('M d, Y \a\t H:i', strtotime($offer['created_at'])) ?>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Actions -->
                        <div class="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
                            <div class="flex space-x-3 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                                <a href="/offers/<?= $offer['id'] ?>" class="text-blue-600 hover:text-blue-500 font-medium">
                                    <?= $translations['view_details'] ?? 'View Details' ?>
                                </a>
                                
                                <a href="/messages/thread/<?= $offer['id'] ?>" class="text-gray-600 hover:text-gray-500 font-medium">
                                    <i class="fas fa-comment <?= $isRTL ? 'ml-1' : 'mr-1' ?>"></i>
                                    <?= $translations['messages'] ?? 'Messages' ?>
                                </a>
                            </div>
                            
                            <div class="flex space-x-2 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                                <?php if ($offer['status'] === 'pending'): ?>
                                    <?php if ($auth->id() == $offer['receiver_id']): ?>
                                        <!-- Receiver can accept/reject -->
                                        <form class="ajax-form inline" action="/offers/<?= $offer['id'] ?>/accept" method="POST">
                                            <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                                                <?= $translations['accept'] ?? 'Accept' ?>
                                            </button>
                                        </form>
                                        <form class="ajax-form inline" action="/offers/<?= $offer['id'] ?>/reject" method="POST">
                                            <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm">
                                                <?= $translations['reject'] ?? 'Reject' ?>
                                            </button>
                                        </form>
                                    <?php elseif ($auth->id() == $offer['proposer_id']): ?>
                                        <!-- Proposer can cancel -->
                                        <form class="ajax-form inline" action="/offers/<?= $offer['id'] ?>/cancel" method="POST">
                                            <button type="submit" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm">
                                                <?= $translations['cancel'] ?? 'Cancel' ?>
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                <?php elseif ($offer['status'] === 'accepted' && $auth->id() == $offer['proposer_id']): ?>
                                    <!-- Accepted offers can be cancelled by proposer -->
                                    <form class="ajax-form inline" action="/offers/<?= $offer['id'] ?>/cancel" method="POST" 
                                          onsubmit="return confirm('<?= $translations['confirm_cancel_accepted'] ?? 'Are you sure you want to cancel this accepted offer?' ?>')">
                                        <button type="submit" class="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1 rounded text-sm">
                                            <?= $translations['cancel'] ?? 'Cancel' ?>
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>

<?php include __DIR__ . '/../layout/footer.php'; ?>
