    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-auto <?= $auth->check() ? 'mb-16 md:mb-0' : '' ?>">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4"><?= $translations['about_us'] ?? 'About Tawssil Pro' ?></h3>
                    <p class="text-gray-300 text-sm">
                        <?= $translations['about_description'] ?? 'Connecting drivers and senders across Morocco for efficient goods transportation.' ?>
                    </p>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4"><?= $translations['quick_links'] ?? 'Quick Links' ?></h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="/trips" class="text-gray-300 hover:text-white"><?= $translations['find_trips'] ?? 'Find Trips' ?></a></li>
                        <li><a href="/shipments" class="text-gray-300 hover:text-white"><?= $translations['find_shipments'] ?? 'Find Shipments' ?></a></li>
                        <li><a href="/map/drivers" class="text-gray-300 hover:text-white"><?= $translations['driver_map'] ?? 'Driver Map' ?></a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4"><?= $translations['support'] ?? 'Support' ?></h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="#" class="text-gray-300 hover:text-white"><?= $translations['help_center'] ?? 'Help Center' ?></a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white"><?= $translations['contact_us'] ?? 'Contact Us' ?></a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white"><?= $translations['safety'] ?? 'Safety' ?></a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4"><?= $translations['connect'] ?? 'Connect' ?></h3>
                    <div class="flex space-x-4 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                        <a href="#" class="text-gray-300 hover:text-white">
                            <i class="fab fa-facebook-f text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white">
                            <i class="fab fa-instagram text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-300 text-sm">
                    © <?= date('Y') ?> Tawssil Pro. <?= $translations['all_rights_reserved'] ?? 'All rights reserved.' ?>
                </p>
                <div class="flex space-x-6 <?= $isRTL ? 'space-x-reverse' : '' ?> mt-4 md:mt-0">
                    <a href="#" class="text-gray-300 hover:text-white text-sm"><?= $translations['privacy_policy'] ?? 'Privacy Policy' ?></a>
                    <a href="#" class="text-gray-300 hover:text-white text-sm"><?= $translations['terms_of_service'] ?? 'Terms of Service' ?></a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="/assets/js/app.js"></script>
    
    <!-- Form submission helper -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add CSRF token to all forms
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            if (!form.querySelector('input[name="csrf_token"]')) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrf_token';
                csrfInput.value = document.querySelector('meta[name="csrf-token"]').content;
                form.appendChild(csrfInput);
            }
        });
    });
    </script>
</body>
</html>
