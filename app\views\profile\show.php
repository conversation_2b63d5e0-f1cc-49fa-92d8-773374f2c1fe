<?php 
$title = $translations['profile'] ?? 'Profile - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';
?>

<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900"><?= $translations['profile'] ?? 'Profile' ?></h1>
                <p class="text-gray-600 mt-2"><?= $translations['manage_profile'] ?? 'Manage your account information and settings' ?></p>
            </div>
            
            <a href="/profile/edit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium transition duration-300">
                <i class="fas fa-edit <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                <?= $translations['edit_profile'] ?? 'Edit Profile' ?>
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Profile Information -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Personal Information -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900"><?= $translations['personal_information'] ?? 'Personal Information' ?></h2>
                </div>
                
                <div class="px-6 py-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">
                                <?= $translations['full_name'] ?? 'Full Name' ?>
                            </label>
                            <p class="text-lg font-medium text-gray-900"><?= htmlspecialchars($user['name']) ?></p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">
                                <?= $translations['email'] ?? 'Email' ?>
                            </label>
                            <p class="text-lg font-medium text-gray-900"><?= htmlspecialchars($user['email']) ?></p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">
                                <?= $translations['phone'] ?? 'Phone' ?>
                            </label>
                            <p class="text-lg font-medium text-gray-900">
                                <?= $user['phone'] ? htmlspecialchars($user['phone']) : '<span class="text-gray-400">' . ($translations['not_provided'] ?? 'Not provided') . '</span>' ?>
                            </p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">
                                <?= $translations['role'] ?? 'Role' ?>
                            </label>
                            <span class="inline-flex px-3 py-1 text-sm font-medium rounded-full <?= 
                                $user['role'] === 'driver' ? 'bg-blue-100 text-blue-800' : 
                                ($user['role'] === 'sender' ? 'bg-green-100 text-green-800' : 'bg-purple-100 text-purple-800')
                            ?>">
                                <?= $translations[$user['role']] ?? ucfirst($user['role']) ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Vehicle Information (for drivers) -->
            <?php if ($user['role'] === 'driver'): ?>
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900"><?= $translations['vehicle_information'] ?? 'Vehicle Information' ?></h2>
                    </div>
                    
                    <div class="px-6 py-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">
                                    <?= $translations['vehicle_type'] ?? 'Vehicle Type' ?>
                                </label>
                                <p class="text-lg font-medium text-gray-900">
                                    <?= $user['vehicle_type'] ? htmlspecialchars($user['vehicle_type']) : '<span class="text-gray-400">' . ($translations['not_provided'] ?? 'Not provided') . '</span>' ?>
                                </p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">
                                    <?= $translations['vehicle_plate'] ?? 'Vehicle Plate' ?>
                                </label>
                                <p class="text-lg font-medium text-gray-900">
                                    <?= $user['vehicle_plate'] ? htmlspecialchars($user['vehicle_plate']) : '<span class="text-gray-400">' . ($translations['not_provided'] ?? 'Not provided') . '</span>' ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Account Statistics -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900"><?= $translations['account_statistics'] ?? 'Account Statistics' ?></h2>
                </div>
                
                <div class="px-6 py-6">
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-6">
                        <?php if ($user['role'] === 'driver'): ?>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600"><?= $stats['active_trips'] ?? 0 ?></div>
                                <div class="text-sm text-gray-500"><?= $translations['active_trips'] ?? 'Active Trips' ?></div>
                            </div>
                            
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600"><?= $stats['completed_trips'] ?? 0 ?></div>
                                <div class="text-sm text-gray-500"><?= $translations['completed_trips'] ?? 'Completed Trips' ?></div>
                            </div>
                            
                            <div class="text-center">
                                <div class="text-2xl font-bold text-gray-600"><?= $stats['total_trips'] ?? 0 ?></div>
                                <div class="text-sm text-gray-500"><?= $translations['total_trips'] ?? 'Total Trips' ?></div>
                            </div>
                        <?php else: ?>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600"><?= $stats['active_shipments'] ?? 0 ?></div>
                                <div class="text-sm text-gray-500"><?= $translations['active_shipments'] ?? 'Active Shipments' ?></div>
                            </div>
                            
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600"><?= $stats['completed_shipments'] ?? 0 ?></div>
                                <div class="text-sm text-gray-500"><?= $translations['completed_shipments'] ?? 'Completed Shipments' ?></div>
                            </div>
                            
                            <div class="text-center">
                                <div class="text-2xl font-bold text-gray-600"><?= $stats['total_shipments'] ?? 0 ?></div>
                                <div class="text-sm text-gray-500"><?= $translations['total_shipments'] ?? 'Total Shipments' ?></div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Sidebar -->
        <div class="space-y-6">
            <!-- Avatar Card -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-6 text-center">
                    <div class="mb-4">
                        <?php if ($user['avatar']): ?>
                            <img src="/uploads/<?= htmlspecialchars($user['avatar']) ?>" 
                                 alt="Profile Picture" 
                                 class="w-24 h-24 rounded-full mx-auto object-cover">
                        <?php else: ?>
                            <div class="w-24 h-24 bg-gray-200 rounded-full mx-auto flex items-center justify-center">
                                <i class="fas fa-user text-gray-500 text-2xl"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <h3 class="text-lg font-medium text-gray-900"><?= htmlspecialchars($user['name']) ?></h3>
                    <p class="text-sm text-gray-500"><?= htmlspecialchars($user['email']) ?></p>
                    
                    <div class="mt-4">
                        <span class="inline-flex px-3 py-1 text-sm font-medium rounded-full <?= 
                            $user['role'] === 'driver' ? 'bg-blue-100 text-blue-800' : 
                            ($user['role'] === 'sender' ? 'bg-green-100 text-green-800' : 'bg-purple-100 text-purple-800')
                        ?>">
                            <?= $translations[$user['role']] ?? ucfirst($user['role']) ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900"><?= $translations['quick_actions'] ?? 'Quick Actions' ?></h2>
                </div>
                
                <div class="px-6 py-6 space-y-3">
                    <?php if ($user['role'] === 'driver'): ?>
                        <a href="/trips/create" class="w-full bg-blue-600 hover:bg-blue-700 text-white text-center px-4 py-2 rounded-md text-sm font-medium inline-block">
                            <i class="fas fa-plus <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                            <?= $translations['post_trip'] ?? 'Post Trip' ?>
                        </a>
                        
                        <a href="/shipments" class="w-full border border-gray-300 text-gray-700 hover:bg-gray-50 text-center px-4 py-2 rounded-md text-sm font-medium inline-block">
                            <i class="fas fa-search <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                            <?= $translations['find_shipments'] ?? 'Find Shipments' ?>
                        </a>
                    <?php else: ?>
                        <a href="/shipments/create" class="w-full bg-green-600 hover:bg-green-700 text-white text-center px-4 py-2 rounded-md text-sm font-medium inline-block">
                            <i class="fas fa-plus <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                            <?= $translations['post_shipment'] ?? 'Post Shipment' ?>
                        </a>
                        
                        <a href="/trips" class="w-full border border-gray-300 text-gray-700 hover:bg-gray-50 text-center px-4 py-2 rounded-md text-sm font-medium inline-block">
                            <i class="fas fa-search <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                            <?= $translations['find_trips'] ?? 'Find Trips' ?>
                        </a>
                    <?php endif; ?>
                    
                    <a href="/offers" class="w-full border border-gray-300 text-gray-700 hover:bg-gray-50 text-center px-4 py-2 rounded-md text-sm font-medium inline-block">
                        <i class="fas fa-handshake <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                        <?= $translations['view_offers'] ?? 'View Offers' ?>
                    </a>
                    
                    <a href="/messages" class="w-full border border-gray-300 text-gray-700 hover:bg-gray-50 text-center px-4 py-2 rounded-md text-sm font-medium inline-block">
                        <i class="fas fa-comment <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                        <?= $translations['messages'] ?? 'Messages' ?>
                    </a>
                </div>
            </div>

            <!-- Account Information -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900"><?= $translations['account_info'] ?? 'Account Information' ?></h2>
                </div>
                
                <div class="px-6 py-6 space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-500"><?= $translations['member_since'] ?? 'Member since' ?>:</span>
                        <span class="font-medium"><?= date('M Y', strtotime($user['created_at'])) ?></span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-500"><?= $translations['last_updated'] ?? 'Last updated' ?>:</span>
                        <span class="font-medium"><?= date('M d, Y', strtotime($user['updated_at'])) ?></span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-500"><?= $translations['account_status'] ?? 'Account status' ?>:</span>
                        <span class="font-medium text-green-600"><?= $translations['active'] ?? 'Active' ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../layout/footer.php'; ?>
