<?php 
$title = $translations['shipment_details'] ?? 'Shipment Details - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                    <?= htmlspecialchars($shipment['from_city']) ?> → <?= htmlspecialchars($shipment['to_city']) ?>
                </h1>
                <p class="text-gray-600"><?= $translations['shipment_details_subtitle'] ?? 'Shipment details and information' ?></p>
            </div>
            
            <?php if ($auth->id() == $shipment['user_id']): ?>
                <div class="flex space-x-3 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                    <a href="/shipments/<?= $shipment['id'] ?>/edit" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-edit <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                        <?= $translations['edit'] ?? 'Edit' ?>
                    </a>
                    
                    <?php if ($shipment['status'] === 'open'): ?>
                        <form class="ajax-form inline" action="/shipments/<?= $shipment['id'] ?>/delete" method="POST" 
                              onsubmit="return confirm('<?= $translations['confirm_delete_shipment'] ?? 'Are you sure you want to cancel this shipment?' ?>')">
                            <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-times <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                                <?= $translations['cancel_shipment'] ?? 'Cancel Shipment' ?>
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Shipment Information -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Main Details -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900"><?= $translations['shipment_information'] ?? 'Shipment Information' ?></h2>
                </div>
                
                <div class="px-6 py-6">
                    <!-- Route -->
                    <div class="flex items-center mb-6">
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <div class="text-center">
                                    <i class="fas fa-map-marker-alt text-green-600 text-2xl"></i>
                                    <p class="text-sm font-medium text-gray-900 mt-1"><?= htmlspecialchars($shipment['from_city']) ?></p>
                                    <p class="text-xs text-gray-500"><?= $translations['pickup'] ?? 'Pickup' ?></p>
                                </div>
                                
                                <div class="flex-1 mx-4">
                                    <div class="border-t-2 border-dashed border-gray-300 relative">
                                        <i class="fas fa-box absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-green-600 bg-white px-2"></i>
                                    </div>
                                </div>
                                
                                <div class="text-center">
                                    <i class="fas fa-map-marker-alt text-red-600 text-2xl"></i>
                                    <p class="text-sm font-medium text-gray-900 mt-1"><?= htmlspecialchars($shipment['to_city']) ?></p>
                                    <p class="text-xs text-gray-500"><?= $translations['delivery'] ?? 'Delivery' ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Shipment Details Grid -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                        <div class="text-center">
                            <i class="fas fa-calendar text-green-600 text-xl mb-2"></i>
                            <p class="text-sm text-gray-500"><?= $translations['ready_date'] ?? 'Ready Date' ?></p>
                            <p class="font-medium"><?= date('M d, Y', strtotime($shipment['ready_date'])) ?></p>
                        </div>
                        
                        <div class="text-center">
                            <i class="fas fa-weight-hanging text-purple-600 text-xl mb-2"></i>
                            <p class="text-sm text-gray-500"><?= $translations['weight'] ?? 'Weight' ?></p>
                            <p class="font-medium"><?= $shipment['weight_kg'] ?> kg</p>
                        </div>
                        
                        <div class="text-center">
                            <i class="fas fa-shield-alt text-orange-600 text-xl mb-2"></i>
                            <p class="text-sm text-gray-500"><?= $translations['fragile'] ?? 'Fragile' ?></p>
                            <p class="font-medium">
                                <?php if ($shipment['fragile']): ?>
                                    <span class="text-orange-600"><?= $translations['yes'] ?? 'Yes' ?></span>
                                <?php else: ?>
                                    <span class="text-gray-600"><?= $translations['no'] ?? 'No' ?></span>
                                <?php endif; ?>
                            </p>
                        </div>
                        
                        <div class="text-center">
                            <i class="fas fa-info-circle text-gray-600 text-xl mb-2"></i>
                            <p class="text-sm text-gray-500"><?= $translations['status'] ?? 'Status' ?></p>
                            <span class="px-2 py-1 text-xs font-medium rounded-full <?= 
                                $shipment['status'] === 'open' ? 'bg-green-100 text-green-800' : 
                                ($shipment['status'] === 'matched' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800')
                            ?>">
                                <?= $translations['status_' . $shipment['status']] ?? ucfirst($shipment['status']) ?>
                            </span>
                        </div>
                    </div>
                    
                    <!-- Description -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h3 class="text-sm font-medium text-gray-900 mb-2"><?= $translations['description'] ?? 'Description' ?></h3>
                        <p class="text-gray-700"><?= nl2br(htmlspecialchars($shipment['description'])) ?></p>
                    </div>
                    
                    <!-- Photo -->
                    <?php if ($shipment['photo']): ?>
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <h3 class="text-sm font-medium text-gray-900 mb-2"><?= $translations['shipment_photo'] ?? 'Shipment Photo' ?></h3>
                            <img src="/uploads/<?= htmlspecialchars($shipment['photo']) ?>" 
                                 alt="Shipment photo" 
                                 class="w-full max-w-md h-64 object-cover rounded-lg">
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Offers Section -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h2 class="text-lg font-semibold text-gray-900">
                            <?= $translations['offers'] ?? 'Offers' ?> (<?= count($offers) ?>)
                        </h2>
                        <?php if ($auth->hasRole('driver') && $shipment['status'] === 'open' && $auth->id() != $shipment['user_id']): ?>
                            <button onclick="makeOffer(<?= $shipment['id'] ?>)" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-plus <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                                <?= $translations['make_offer'] ?? 'Make Offer' ?>
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="px-6 py-6">
                    <?php if (empty($offers)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-handshake text-4xl text-gray-300 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2"><?= $translations['no_offers_yet'] ?? 'No offers yet' ?></h3>
                            <p class="text-gray-600"><?= $translations['no_offers_description_shipment'] ?? 'This shipment hasn\'t received any offers from drivers yet.' ?></p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-4">
                            <?php foreach ($offers as $offer): ?>
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <div class="flex items-center mb-2">
                                                <h3 class="font-medium text-gray-900"><?= htmlspecialchars($offer['driver_name']) ?></h3>
                                                <span class="<?= $isRTL ? 'mr-3' : 'ml-3' ?> px-2 py-1 text-xs font-medium rounded-full <?= 
                                                    $offer['status'] === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                                                    ($offer['status'] === 'accepted' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800')
                                                ?>">
                                                    <?= $translations['status_' . $offer['status']] ?? ucfirst($offer['status']) ?>
                                                </span>
                                            </div>
                                            
                                            <div class="grid grid-cols-2 gap-4 mb-3 text-sm">
                                                <div>
                                                    <span class="text-gray-500"><?= $translations['trip'] ?? 'Trip' ?>:</span>
                                                    <p class="font-medium"><?= htmlspecialchars($offer['trip_from']) ?> → <?= htmlspecialchars($offer['trip_to']) ?></p>
                                                </div>
                                                <div>
                                                    <span class="text-gray-500"><?= $translations['trip_date'] ?? 'Trip Date' ?>:</span>
                                                    <p class="font-medium"><?= date('M d, Y', strtotime($offer['trip_date'])) ?></p>
                                                </div>
                                            </div>
                                            
                                            <div class="text-sm text-gray-600">
                                                <i class="fas fa-phone <?= $isRTL ? 'ml-1' : 'mr-1' ?>"></i>
                                                <?= htmlspecialchars($offer['driver_phone']) ?>
                                            </div>
                                        </div>
                                        
                                        <?php if ($offer['price_total']): ?>
                                            <div class="<?= $isRTL ? 'mr-4' : 'ml-4' ?> text-<?= $isRTL ? 'left' : 'right' ?>">
                                                <p class="text-lg font-bold text-green-600"><?= $offer['price_total'] ?> MAD</p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <?php if ($auth->id() == $shipment['user_id'] && $offer['status'] === 'pending'): ?>
                                        <div class="mt-4 pt-4 border-t border-gray-200 flex space-x-3 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                                            <form class="ajax-form inline" action="/offers/<?= $offer['id'] ?>/accept" method="POST">
                                                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                                                    <?= $translations['accept'] ?? 'Accept' ?>
                                                </button>
                                            </form>
                                            <form class="ajax-form inline" action="/offers/<?= $offer['id'] ?>/reject" method="POST">
                                                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm">
                                                    <?= $translations['reject'] ?? 'Reject' ?>
                                                </button>
                                            </form>
                                            <a href="/offers/<?= $offer['id'] ?>" class="text-blue-600 hover:text-blue-500 text-sm">
                                                <?= $translations['view_details'] ?? 'View Details' ?>
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <div class="mt-4 pt-4 border-t border-gray-200">
                                            <a href="/offers/<?= $offer['id'] ?>" class="text-blue-600 hover:text-blue-500 text-sm">
                                                <?= $translations['view_details'] ?? 'View Details' ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sender Information Sidebar -->
        <div class="space-y-6">
            <!-- Sender Card -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900"><?= $translations['sender_information'] ?? 'Sender Information' ?></h2>
                </div>
                
                <div class="px-6 py-6">
                    <div class="text-center mb-4">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-user text-green-600 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900"><?= htmlspecialchars($shipment['sender_name']) ?></h3>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <i class="fas fa-phone text-gray-400 w-5"></i>
                            <span class="<?= $isRTL ? 'mr-3' : 'ml-3' ?> text-gray-700"><?= htmlspecialchars($shipment['sender_phone']) ?></span>
                        </div>
                        
                        <div class="flex items-center">
                            <i class="fas fa-envelope text-gray-400 w-5"></i>
                            <span class="<?= $isRTL ? 'mr-3' : 'ml-3' ?> text-gray-700"><?= htmlspecialchars($shipment['sender_email']) ?></span>
                        </div>
                    </div>
                    
                    <?php if ($auth->check() && $auth->id() != $shipment['user_id']): ?>
                        <div class="mt-6 pt-4 border-t border-gray-200">
                            <a href="/messages" class="w-full bg-green-600 hover:bg-green-700 text-white text-center px-4 py-2 rounded-md text-sm font-medium inline-block">
                                <i class="fas fa-comment <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                                <?= $translations['contact_sender'] ?? 'Contact Sender' ?>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Shipment Summary -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900"><?= $translations['shipment_summary'] ?? 'Shipment Summary' ?></h2>
                </div>
                
                <div class="px-6 py-6 space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-500"><?= $translations['created'] ?? 'Created' ?>:</span>
                        <span class="font-medium"><?= date('M d, Y', strtotime($shipment['created_at'])) ?></span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-500"><?= $translations['updated'] ?? 'Updated' ?>:</span>
                        <span class="font-medium"><?= date('M d, Y', strtotime($shipment['updated_at'])) ?></span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-500"><?= $translations['total_offers'] ?? 'Total Offers' ?>:</span>
                        <span class="font-medium"><?= count($offers) ?></span>
                    </div>
                    
                    <?php if ($shipment['fragile']): ?>
                        <div class="pt-3 border-t border-gray-200">
                            <div class="flex items-center text-orange-600">
                                <i class="fas fa-exclamation-triangle <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                                <span class="font-medium"><?= $translations['fragile_item'] ?? 'Fragile Item' ?></span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1"><?= $translations['fragile_description'] ?? 'Requires special care during transport' ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Make Offer Modal -->
<div id="offerModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['make_offer'] ?? 'Make Offer' ?></h3>
            
            <form id="offerForm" class="ajax-form" action="/offers/create" method="POST">
                <input type="hidden" id="offerShipmentId" name="shipment_id" value="<?= $shipment['id'] ?>">
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2"><?= $translations['total_price'] ?? 'Total Price (MAD)' ?></label>
                    <input type="number" name="price_total" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="<?= $translations['enter_price'] ?? 'Enter your offer price' ?>">
                </div>
                
                <div class="flex justify-center space-x-4 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                    <button type="button" onclick="closeOfferModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        <?= $translations['cancel'] ?? 'Cancel' ?>
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        <?= $translations['send_offer'] ?? 'Send Offer' ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function makeOffer(shipmentId) {
    document.getElementById('offerShipmentId').value = shipmentId;
    document.getElementById('offerModal').classList.remove('hidden');
}

function closeOfferModal() {
    document.getElementById('offerModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('offerModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeOfferModal();
    }
});
</script>

<?php include __DIR__ . '/../layout/footer.php'; ?>
