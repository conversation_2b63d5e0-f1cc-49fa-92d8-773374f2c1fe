<?php
class User {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function findById($id) {
        $stmt = $this->db->query(
            "SELECT * FROM users WHERE id = ? AND deleted_at IS NULL",
            [$id]
        );
        return $stmt->fetch();
    }
    
    public function findByEmail($email) {
        $stmt = $this->db->query(
            "SELECT * FROM users WHERE email = ? AND deleted_at IS NULL",
            [$email]
        );
        return $stmt->fetch();
    }
    
    public function create($data) {
        $stmt = $this->db->query(
            "INSERT INTO users (name, email, phone, password_hash, role, created_at, updated_at) 
             VALUES (?, ?, ?, ?, ?, NOW(), NOW())",
            [
                $data['name'],
                $data['email'],
                $data['phone'] ?? null,
                $data['password_hash'],
                $data['role']
            ]
        );
        
        return $stmt ? $this->db->lastInsertId() : false;
    }
    
    public function update($id, $data) {
        $fields = [];
        $values = [];
        
        foreach ($data as $key => $value) {
            if ($key !== 'id') {
                $fields[] = "{$key} = ?";
                $values[] = $value;
            }
        }
        
        $fields[] = "updated_at = NOW()";
        $values[] = $id;
        
        $sql = "UPDATE users SET " . implode(', ', $fields) . " WHERE id = ?";
        $stmt = $this->db->query($sql, $values);
        
        return $stmt !== false;
    }
    
    public function delete($id) {
        $stmt = $this->db->query(
            "UPDATE users SET deleted_at = NOW() WHERE id = ?",
            [$id]
        );
        
        return $stmt !== false;
    }
    
    public function updateLocation($userId, $latitude, $longitude) {
        $stmt = $this->db->query(
            "UPDATE users SET latitude = ?, longitude = ?, updated_at = NOW() WHERE id = ?",
            [$latitude, $longitude, $userId]
        );
        
        return $stmt !== false;
    }
    
    public function getActiveDrivers() {
        $stmt = $this->db->query(
            "SELECT id, name, vehicle_type, latitude, longitude, updated_at 
             FROM users 
             WHERE role = 'driver' 
             AND deleted_at IS NULL 
             AND latitude IS NOT NULL 
             AND longitude IS NOT NULL
             AND updated_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)"
        );
        
        return $stmt->fetchAll();
    }
    
    public function getAll($filters = []) {
        $sql = "SELECT * FROM users WHERE 1=1";
        $params = [];
        
        if (isset($filters['role'])) {
            $sql .= " AND role = ?";
            $params[] = $filters['role'];
        }
        
        if (isset($filters['search'])) {
            $sql .= " AND (name LIKE ? OR email LIKE ?)";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
        }
        
        if (isset($filters['status']) && $filters['status'] === 'active') {
            $sql .= " AND deleted_at IS NULL";
        } elseif (isset($filters['status']) && $filters['status'] === 'deleted') {
            $sql .= " AND deleted_at IS NOT NULL";
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        $stmt = $this->db->query($sql, $params);
        return $stmt->fetchAll();
    }
}
?>
