<?php 
$title = $translations['login'] ?? 'Login - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';
?>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                <?= $translations['sign_in'] ?? 'Sign in to your account' ?>
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                <?= $translations['no_account'] ?? "Don't have an account?" ?>
                <a href="/register" class="font-medium text-blue-600 hover:text-blue-500">
                    <?= $translations['register_here'] ?? 'Register here' ?>
                </a>
            </p>
        </div>
        
        <?php if (isset($error)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <form class="mt-8 space-y-6 ajax-form" action="/login" method="POST">
            <div class="space-y-4">
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">
                        <?= $translations['email'] ?? 'Email Address' ?>
                    </label>
                    <input id="email" name="email" type="email" required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                           placeholder="<?= $translations['email_placeholder'] ?? 'Enter your email' ?>"
                           value="<?= htmlspecialchars($email ?? '') ?>">
                </div>
                
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700">
                        <?= $translations['password'] ?? 'Password' ?>
                    </label>
                    <input id="password" name="password" type="password" required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                           placeholder="<?= $translations['password_placeholder'] ?? 'Enter your password' ?>">
                </div>
            </div>
            
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input id="remember_me" name="remember_me" type="checkbox" 
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="remember_me" class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> block text-sm text-gray-900">
                        <?= $translations['remember_me'] ?? 'Remember me' ?>
                    </label>
                </div>
                
                <div class="text-sm">
                    <a href="#" class="font-medium text-blue-600 hover:text-blue-500">
                        <?= $translations['forgot_password'] ?? 'Forgot your password?' ?>
                    </a>
                </div>
            </div>
            
            <div>
                <button type="submit" 
                        data-original-text="<?= $translations['sign_in'] ?? 'Sign in' ?>"
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <span class="absolute <?= $isRTL ? 'right' : 'left' ?>-0 inset-y-0 flex items-center <?= $isRTL ? 'pr' : 'pl' ?>-3">
                        <i class="fas fa-sign-in-alt text-blue-500 group-hover:text-blue-400"></i>
                    </span>
                    <?= $translations['sign_in'] ?? 'Sign in' ?>
                </button>
            </div>
        </form>
        
        <!-- Demo Credentials -->
        <div class="mt-6 p-4 bg-gray-100 rounded-md">
            <h3 class="text-sm font-medium text-gray-900 mb-2"><?= $translations['demo_credentials'] ?? 'Demo Credentials:' ?></h3>
            <div class="text-xs text-gray-600 space-y-1">
                <div><strong><?= $translations['admin'] ?? 'Admin' ?>:</strong> <EMAIL> / Admin@123</div>
                <div><strong><?= $translations['driver'] ?? 'Driver' ?>:</strong> <EMAIL> / Driver@123</div>
                <div><strong><?= $translations['sender'] ?? 'Sender' ?>:</strong> <EMAIL> / Sender@123</div>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../layout/footer.php'; ?>
