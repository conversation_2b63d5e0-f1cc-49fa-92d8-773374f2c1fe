<?php
class Notification {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function create($data) {
        $stmt = $this->db->query(
            "INSERT INTO notifications (user_id, type, ref_id, created_at) 
             VALUES (?, ?, ?, NOW())",
            [
                $data['user_id'],
                $data['type'],
                $data['ref_id'] ?? null
            ]
        );
        
        return $stmt ? $this->db->lastInsertId() : false;
    }
    
    public function getByUser($userId, $limit = 50) {
        $stmt = $this->db->query(
            "SELECT n.*, 
                    CASE n.type
                        WHEN 'new_offer' THEN 'New offer received'
                        WHEN 'offer_accepted' THEN 'Your offer was accepted'
                        WHEN 'offer_rejected' THEN 'Your offer was rejected'
                        WHEN 'offer_cancelled' THEN 'Offer was cancelled'
                        WHEN 'new_message' THEN 'New message received'
                        ELSE n.type
                    END as title
             FROM notifications n
             WHERE n.user_id = ?
             ORDER BY n.created_at DESC
             LIMIT ?",
            [$userId, $limit]
        );
        
        return $stmt->fetchAll();
    }
    
    public function getUnreadCount($userId) {
        $stmt = $this->db->query(
            "SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND seen = 0",
            [$userId]
        );
        
        $result = $stmt->fetch();
        return (int)$result['count'];
    }
    
    public function markAsRead($id, $userId) {
        $stmt = $this->db->query(
            "UPDATE notifications SET seen = 1 WHERE id = ? AND user_id = ?",
            [$id, $userId]
        );
        
        return $stmt !== false;
    }
    
    public function markAllRead($userId) {
        $stmt = $this->db->query(
            "UPDATE notifications SET seen = 1 WHERE user_id = ?",
            [$userId]
        );
        
        return $stmt !== false;
    }
}
?>
