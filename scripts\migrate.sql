-- Tawssil Pro Database Migration Script
-- MySQL 8.x compatible

-- Create database
CREATE DATABASE IF NOT EXISTS tawssil_pro CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE tawssil_pro;

-- Drop tables if they exist (for fresh installation)
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS notifications;
DROP TABLE IF EXISTS messages;
DROP TABLE IF EXISTS message_reads;
DROP TABLE IF EXISTS offers;
DROP TABLE IF EXISTS shipments;
DROP TABLE IF EXISTS trips;
DROP TABLE IF EXISTS users;
SET FOREIGN_KEY_CHECKS = 1;

-- Users table
CREATE TABLE users (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    phone VARCHAR(20) NULL,
    password_hash VARCHAR(255) NOT NULL,
    role <PERSON><PERSON><PERSON>('driver', 'sender', 'admin') NOT NULL DEFAULT 'sender',
    avatar VARCHAR(255) NULL,
    vehicle_type VARCHAR(100) NULL,
    vehicle_plate VARCHAR(50) NULL,
    latitude DECIMAL(10, 8) NULL,
    longitude DECIMAL(11, 8) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_location (latitude, longitude),
    INDEX idx_deleted (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Trips table
CREATE TABLE trips (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    from_city VARCHAR(100) NOT NULL,
    to_city VARCHAR(100) NOT NULL,
    trip_date DATE NOT NULL,
    capacity_kg DECIMAL(8,2) NOT NULL,
    price_per_kg DECIMAL(8,2) NOT NULL,
    notes TEXT NULL,
    status ENUM('open', 'full', 'cancelled') DEFAULT 'open',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_route_date (from_city, to_city, trip_date),
    INDEX idx_status (status),
    INDEX idx_date (trip_date),
    INDEX idx_price (price_per_kg)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Shipments table
CREATE TABLE shipments (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    from_city VARCHAR(100) NOT NULL,
    to_city VARCHAR(100) NOT NULL,
    ready_date DATE NOT NULL,
    weight_kg DECIMAL(8,2) NOT NULL,
    description TEXT NOT NULL,
    fragile TINYINT(1) DEFAULT 0,
    photo VARCHAR(255) NULL,
    status ENUM('open', 'matched', 'delivered', 'cancelled') DEFAULT 'open',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_route_date (from_city, to_city, ready_date),
    INDEX idx_status (status),
    INDEX idx_date (ready_date),
    INDEX idx_weight (weight_kg),
    INDEX idx_fragile (fragile)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Offers table
CREATE TABLE offers (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    trip_id INT UNSIGNED NULL,
    shipment_id INT UNSIGNED NULL,
    proposer_id INT UNSIGNED NOT NULL,
    receiver_id INT UNSIGNED NOT NULL,
    price_total DECIMAL(10,2) NULL,
    status ENUM('pending', 'accepted', 'rejected', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (trip_id) REFERENCES trips(id) ON DELETE CASCADE,
    FOREIGN KEY (shipment_id) REFERENCES shipments(id) ON DELETE CASCADE,
    FOREIGN KEY (proposer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_trip (trip_id),
    INDEX idx_shipment (shipment_id),
    INDEX idx_proposer (proposer_id),
    INDEX idx_receiver_status (receiver_id, status),
    INDEX idx_status (status),
    CONSTRAINT chk_offer_type CHECK (
        (trip_id IS NOT NULL AND shipment_id IS NULL) OR 
        (trip_id IS NULL AND shipment_id IS NOT NULL)
    )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Messages table
CREATE TABLE messages (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    offer_id INT UNSIGNED NOT NULL,
    sender_id INT UNSIGNED NOT NULL,
    receiver_id INT UNSIGNED NOT NULL,
    body TEXT NOT NULL,
    attachment VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_offer_date (offer_id, created_at),
    INDEX idx_sender (sender_id),
    INDEX idx_receiver (receiver_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Message reads tracking table
CREATE TABLE message_reads (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    offer_id INT UNSIGNED NOT NULL,
    seen_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_offer (user_id, offer_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Notifications table
CREATE TABLE notifications (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    type VARCHAR(50) NOT NULL,
    ref_id INT UNSIGNED NULL,
    seen TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_seen (user_id, seen),
    INDEX idx_created (created_at),
    INDEX idx_type (type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create additional indexes for performance
CREATE INDEX idx_trips_search ON trips (from_city, to_city, trip_date, status);
CREATE INDEX idx_shipments_search ON shipments (from_city, to_city, ready_date, status);
CREATE INDEX idx_offers_participants ON offers (proposer_id, receiver_id, status);
CREATE INDEX idx_messages_conversation ON messages (offer_id, created_at DESC);
CREATE INDEX idx_notifications_unread ON notifications (user_id, seen, created_at DESC);

-- Full text search indexes for better search functionality
ALTER TABLE trips ADD FULLTEXT(from_city, to_city, notes);
ALTER TABLE shipments ADD FULLTEXT(from_city, to_city, description);
ALTER TABLE users ADD FULLTEXT(name);

-- Views for common queries
CREATE VIEW active_trips AS
SELECT 
    t.*,
    u.name as driver_name,
    u.phone as driver_phone,
    u.email as driver_email,
    u.vehicle_type,
    u.vehicle_plate
FROM trips t
JOIN users u ON t.user_id = u.id
WHERE t.status = 'open' 
AND u.deleted_at IS NULL;

CREATE VIEW active_shipments AS
SELECT 
    s.*,
    u.name as sender_name,
    u.phone as sender_phone,
    u.email as sender_email
FROM shipments s
JOIN users u ON s.user_id = u.id
WHERE s.status = 'open'
AND u.deleted_at IS NULL;

CREATE VIEW offer_details AS
SELECT 
    o.*,
    proposer.name as proposer_name,
    proposer.email as proposer_email,
    proposer.phone as proposer_phone,
    receiver.name as receiver_name,
    receiver.email as receiver_email,
    receiver.phone as receiver_phone,
    CASE 
        WHEN o.trip_id IS NOT NULL THEN CONCAT(t.from_city, ' → ', t.to_city)
        ELSE CONCAT(s.from_city, ' → ', s.to_city)
    END as route,
    CASE 
        WHEN o.trip_id IS NOT NULL THEN t.trip_date
        ELSE s.ready_date
    END as transport_date
FROM offers o
LEFT JOIN trips t ON o.trip_id = t.id
LEFT JOIN shipments s ON o.shipment_id = s.id
JOIN users proposer ON o.proposer_id = proposer.id
JOIN users receiver ON o.receiver_id = receiver.id;

-- Triggers for automatic actions

-- Update trip status when offer is accepted
DELIMITER //
CREATE TRIGGER update_trip_status_on_offer_accept
    AFTER UPDATE ON offers
    FOR EACH ROW
BEGIN
    IF NEW.status = 'accepted' AND OLD.status = 'pending' AND NEW.trip_id IS NOT NULL THEN
        UPDATE trips SET status = 'full' WHERE id = NEW.trip_id;
    END IF;
    
    IF NEW.status = 'accepted' AND OLD.status = 'pending' AND NEW.shipment_id IS NOT NULL THEN
        UPDATE shipments SET status = 'matched' WHERE id = NEW.shipment_id;
    END IF;
END//

-- Reopen trip/shipment when accepted offer is cancelled
CREATE TRIGGER reopen_on_offer_cancel
    AFTER UPDATE ON offers
    FOR EACH ROW
BEGIN
    IF NEW.status = 'cancelled' AND OLD.status = 'accepted' THEN
        IF NEW.trip_id IS NOT NULL THEN
            UPDATE trips SET status = 'open' WHERE id = NEW.trip_id;
        END IF;
        
        IF NEW.shipment_id IS NOT NULL THEN
            UPDATE shipments SET status = 'open' WHERE id = NEW.shipment_id;
        END IF;
    END IF;
END//

DELIMITER ;

-- Insert default admin user (password: Admin@123)
INSERT INTO users (name, email, password_hash, role, created_at, updated_at) VALUES
('System Administrator', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', NOW(), NOW());

-- Grant privileges (adjust according to your setup)
-- GRANT ALL PRIVILEGES ON tawssil_pro.* TO 'tawssil_user'@'localhost' IDENTIFIED BY 'secure_password';
-- FLUSH PRIVILEGES;

-- Performance optimization settings
SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';

-- Verify tables creation
SHOW TABLES;

-- Show table structures for verification
DESCRIBE users;
DESCRIBE trips;
DESCRIBE shipments;
DESCRIBE offers;
DESCRIBE messages;
DESCRIBE notifications;

-- Display statistics
SELECT 
    'Migration completed successfully. Database ready for use.' as status,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'tawssil_pro') as tables_created,
    (SELECT COUNT(*) FROM information_schema.views WHERE table_schema = 'tawssil_pro') as views_created;
