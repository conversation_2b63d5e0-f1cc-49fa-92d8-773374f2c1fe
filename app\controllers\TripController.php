<?php
class TripController extends Controller {
    
    public function index() {
        $this->requireAuth();
        
        $userId = $this->auth->id();
        $userRole = $_SESSION['user']['role'];
        
        // Search parameters
        $fromCity = $_GET['from_city'] ?? '';
        $toCity = $_GET['to_city'] ?? '';
        $date = $_GET['date'] ?? '';
        $maxPrice = $_GET['max_price'] ?? '';
        
        $sql = "SELECT t.*, u.name as driver_name, u.phone as driver_phone 
                FROM trips t 
                JOIN users u ON t.user_id = u.id 
                WHERE t.status = 'open'";
        $params = [];
        
        // If driver, show only their trips
        if ($userRole === 'driver') {
            $sql .= " AND t.user_id = ?";
            $params[] = $userId;
        }
        
        // Apply search filters
        if ($fromCity) {
            $sql .= " AND t.from_city LIKE ?";
            $params[] = "%{$fromCity}%";
        }
        
        if ($toCity) {
            $sql .= " AND t.to_city LIKE ?";
            $params[] = "%{$toCity}%";
        }
        
        if ($date) {
            $sql .= " AND t.trip_date >= ?";
            $params[] = $date;
        }
        
        if ($maxPrice) {
            $sql .= " AND t.price_per_kg <= ?";
            $params[] = $maxPrice;
        }
        
        $sql .= " ORDER BY t.trip_date ASC, t.created_at DESC";
        
        $stmt = $this->db->query($sql, $params);
        $trips = $stmt->fetchAll();
        
        $this->view('trips/index', compact('trips'));
    }
    
    public function create() {
        $this->requireAuth(['driver']);
        $this->view('trips/create');
    }
    
    public function store() {
        $this->requireAuth(['driver']);
        $this->validateCsrf();
        
        $data = [
            'from_city' => $_POST['from_city'] ?? '',
            'to_city' => $_POST['to_city'] ?? '',
            'trip_date' => $_POST['trip_date'] ?? '',
            'capacity_kg' => $_POST['capacity_kg'] ?? 0,
            'price_per_kg' => $_POST['price_per_kg'] ?? 0,
            'notes' => $_POST['notes'] ?? ''
        ];
        
        // Validation
        $errors = [];
        
        if (empty($data['from_city'])) {
            $errors['from_city'] = 'From city is required';
        }
        
        if (empty($data['to_city'])) {
            $errors['to_city'] = 'To city is required';
        }
        
        if (empty($data['trip_date'])) {
            $errors['trip_date'] = 'Trip date is required';
        } elseif (strtotime($data['trip_date']) < strtotime('today')) {
            $errors['trip_date'] = 'Trip date cannot be in the past';
        }
        
        if (empty($data['capacity_kg']) || $data['capacity_kg'] <= 0) {
            $errors['capacity_kg'] = 'Capacity must be greater than 0';
        }
        
        if (empty($data['price_per_kg']) || $data['price_per_kg'] <= 0) {
            $errors['price_per_kg'] = 'Price per kg must be greater than 0';
        }
        
        if (!empty($errors)) {
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'errors' => $errors]);
            } else {
                $this->view('trips/create', ['errors' => $errors, 'data' => $data]);
                return;
            }
        }
        
        // Insert trip
        $stmt = $this->db->query(
            "INSERT INTO trips (user_id, from_city, to_city, trip_date, capacity_kg, price_per_kg, notes, created_at, updated_at) 
             VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())",
            [
                $this->auth->id(),
                $data['from_city'],
                $data['to_city'],
                $data['trip_date'],
                $data['capacity_kg'],
                $data['price_per_kg'],
                $data['notes']
            ]
        );
        
        if ($stmt) {
            $message = 'Trip created successfully';
            if ($this->isAjaxRequest()) {
                $this->json(['success' => true, 'message' => $message, 'redirect' => '/trips']);
            } else {
                $this->redirect('/trips');
            }
        } else {
            $message = 'Failed to create trip';
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => $message]);
            } else {
                $this->view('trips/create', ['error' => $message, 'data' => $data]);
            }
        }
    }
    
    public function show($id) {
        $this->requireAuth();
        
        $stmt = $this->db->query(
            "SELECT t.*, u.name as driver_name, u.phone as driver_phone, u.email as driver_email,
                    u.vehicle_type, u.vehicle_plate
             FROM trips t 
             JOIN users u ON t.user_id = u.id 
             WHERE t.id = ?",
            [$id]
        );
        
        $trip = $stmt->fetch();
        
        if (!$trip) {
            $this->redirect('/trips');
        }
        
        // Get related offers
        $offersStmt = $this->db->query(
            "SELECT o.*, s.description as shipment_description, s.weight_kg,
                    u.name as sender_name, u.phone as sender_phone
             FROM offers o
             JOIN shipments s ON o.shipment_id = s.id
             JOIN users u ON o.proposer_id = u.id
             WHERE o.trip_id = ?
             ORDER BY o.created_at DESC",
            [$id]
        );
        
        $offers = $offersStmt->fetchAll();
        
        $this->view('trips/show', compact('trip', 'offers'));
    }
    
    public function edit($id) {
        $this->requireAuth(['driver']);
        
        $stmt = $this->db->query(
            "SELECT * FROM trips WHERE id = ? AND user_id = ?",
            [$id, $this->auth->id()]
        );
        
        $trip = $stmt->fetch();
        
        if (!$trip) {
            $this->redirect('/trips');
        }
        
        $this->view('trips/edit', compact('trip'));
    }
    
    public function update($id) {
        $this->requireAuth(['driver']);
        $this->validateCsrf();
        
        // Check ownership
        $stmt = $this->db->query(
            "SELECT * FROM trips WHERE id = ? AND user_id = ?",
            [$id, $this->auth->id()]
        );
        
        $trip = $stmt->fetch();
        
        if (!$trip) {
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => 'Trip not found'], 404);
            } else {
                $this->redirect('/trips');
            }
        }
        
        $data = [
            'from_city' => $_POST['from_city'] ?? '',
            'to_city' => $_POST['to_city'] ?? '',
            'trip_date' => $_POST['trip_date'] ?? '',
            'capacity_kg' => $_POST['capacity_kg'] ?? 0,
            'price_per_kg' => $_POST['price_per_kg'] ?? 0,
            'notes' => $_POST['notes'] ?? ''
        ];
        
        // Validation (same as store method)
        $errors = [];
        
        if (empty($data['from_city'])) {
            $errors['from_city'] = 'From city is required';
        }
        
        if (empty($data['to_city'])) {
            $errors['to_city'] = 'To city is required';
        }
        
        if (empty($data['trip_date'])) {
            $errors['trip_date'] = 'Trip date is required';
        }
        
        if (empty($data['capacity_kg']) || $data['capacity_kg'] <= 0) {
            $errors['capacity_kg'] = 'Capacity must be greater than 0';
        }
        
        if (empty($data['price_per_kg']) || $data['price_per_kg'] <= 0) {
            $errors['price_per_kg'] = 'Price per kg must be greater than 0';
        }
        
        if (!empty($errors)) {
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'errors' => $errors]);
            } else {
                $this->view('trips/edit', ['errors' => $errors, 'trip' => array_merge($trip, $data)]);
                return;
            }
        }
        
        // Update trip
        $stmt = $this->db->query(
            "UPDATE trips SET from_city = ?, to_city = ?, trip_date = ?, capacity_kg = ?, 
             price_per_kg = ?, notes = ?, updated_at = NOW()
             WHERE id = ? AND user_id = ?",
            [
                $data['from_city'],
                $data['to_city'],
                $data['trip_date'],
                $data['capacity_kg'],
                $data['price_per_kg'],
                $data['notes'],
                $id,
                $this->auth->id()
            ]
        );
        
        if ($stmt) {
            $message = 'Trip updated successfully';
            if ($this->isAjaxRequest()) {
                $this->json(['success' => true, 'message' => $message, 'redirect' => "/trips/{$id}"]);
            } else {
                $this->redirect("/trips/{$id}");
            }
        } else {
            $message = 'Failed to update trip';
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => $message]);
            } else {
                $this->view('trips/edit', ['error' => $message, 'trip' => array_merge($trip, $data)]);
            }
        }
    }
    
    public function delete($id) {
        $this->requireAuth(['driver']);
        $this->validateCsrf();
        
        // Check ownership
        $stmt = $this->db->query(
            "SELECT * FROM trips WHERE id = ? AND user_id = ?",
            [$id, $this->auth->id()]
        );
        
        $trip = $stmt->fetch();
        
        if (!$trip) {
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => 'Trip not found'], 404);
            } else {
                $this->redirect('/trips');
            }
        }
        
        // Update status to cancelled instead of deleting
        $stmt = $this->db->query(
            "UPDATE trips SET status = 'cancelled', updated_at = NOW() WHERE id = ?",
            [$id]
        );
        
        if ($stmt) {
            $message = 'Trip cancelled successfully';
            if ($this->isAjaxRequest()) {
                $this->json(['success' => true, 'message' => $message, 'redirect' => '/trips']);
            } else {
                $this->redirect('/trips');
            }
        } else {
            $message = 'Failed to cancel trip';
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => $message]);
            } else {
                $this->redirect("/trips/{$id}");
            }
        }
    }
}
?>
