<?php 
$title = $translations['shipments'] ?? 'Shipments - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900"><?= $translations['shipments'] ?? 'Shipments' ?></h1>
                <p class="text-gray-600 mt-2"><?= $translations['find_shipments_to_transport'] ?? 'Find shipments available for transport' ?></p>
            </div>
            <?php if ($auth->hasRole('sender')): ?>
                <a href="/shipments/create" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition duration-300">
                    <i class="fas fa-plus <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                    <?= $translations['post_shipment'] ?? 'Post Shipment' ?>
                </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Search Filters -->
    <div class="bg-white p-6 rounded-lg shadow mb-8">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2"><?= $translations['from_city'] ?? 'From City' ?></label>
                <input type="text" name="from_city" value="<?= htmlspecialchars($_GET['from_city'] ?? '') ?>" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                       placeholder="<?= $translations['enter_pickup_city'] ?? 'Enter pickup city' ?>">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2"><?= $translations['to_city'] ?? 'To City' ?></label>
                <input type="text" name="to_city" value="<?= htmlspecialchars($_GET['to_city'] ?? '') ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                       placeholder="<?= $translations['enter_delivery_city'] ?? 'Enter delivery city' ?>">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2"><?= $translations['ready_date'] ?? 'Ready Date' ?></label>
                <input type="date" name="date" value="<?= htmlspecialchars($_GET['date'] ?? '') ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2"><?= $translations['max_weight'] ?? 'Max Weight (kg)' ?></label>
                <input type="number" name="max_weight" value="<?= htmlspecialchars($_GET['max_weight'] ?? '') ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                       placeholder="kg">
            </div>
            
            <div class="md:col-span-4">
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md transition duration-300">
                    <i class="fas fa-search <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                    <?= $translations['search'] ?? 'Search' ?>
                </button>
                <a href="/shipments" class="<?= $isRTL ? 'mr-4' : 'ml-4' ?> text-gray-600 hover:text-gray-800">
                    <?= $translations['clear_filters'] ?? 'Clear Filters' ?>
                </a>
            </div>
        </form>
    </div>

    <!-- Shipments List -->
    <div class="space-y-6">
        <?php if (empty($shipments)): ?>
            <div class="bg-white rounded-lg shadow p-8 text-center">
                <i class="fas fa-box text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2"><?= $translations['no_shipments_found'] ?? 'No shipments found' ?></h3>
                <p class="text-gray-600"><?= $translations['try_different_search_shipments'] ?? 'Try adjusting your search criteria or check back later.' ?></p>
                <?php if ($auth->hasRole('sender')): ?>
                    <a href="/shipments/create" class="mt-4 inline-block bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md transition duration-300">
                        <?= $translations['post_first_shipment'] ?? 'Post Your First Shipment' ?>
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <?php foreach ($shipments as $shipment): ?>
                <div class="bg-white rounded-lg shadow hover:shadow-lg transition duration-300">
                    <div class="p-6">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center mb-3">
                                    <div class="flex items-center text-lg font-semibold text-gray-900">
                                        <i class="fas fa-map-marker-alt text-green-600 <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                                        <?= htmlspecialchars($shipment['from_city']) ?>
                                        <i class="fas fa-arrow-<?= $isRTL ? 'left' : 'right' ?> text-gray-400 mx-3"></i>
                                        <i class="fas fa-map-marker-alt text-red-600 <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                                        <?= htmlspecialchars($shipment['to_city']) ?>
                                    </div>
                                    <?php if ($shipment['fragile']): ?>
                                        <span class="<?= $isRTL ? 'mr-3' : 'ml-3' ?> px-2 py-1 text-xs font-medium bg-orange-100 text-orange-800 rounded-full">
                                            <i class="fas fa-exclamation-triangle <?= $isRTL ? 'ml-1' : 'mr-1' ?>"></i>
                                            <?= $translations['fragile'] ?? 'Fragile' ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                                    <div>
                                        <span class="text-sm text-gray-500"><?= $translations['ready_date'] ?? 'Ready Date' ?>:</span>
                                        <p class="font-medium"><?= date('M d, Y', strtotime($shipment['ready_date'])) ?></p>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-500"><?= $translations['weight'] ?? 'Weight' ?>:</span>
                                        <p class="font-medium"><?= $shipment['weight_kg'] ?> kg</p>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-500"><?= $translations['sender'] ?? 'Sender' ?>:</span>
                                        <p class="font-medium"><?= htmlspecialchars($shipment['sender_name']) ?></p>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-500"><?= $translations['status'] ?? 'Status' ?>:</span>
                                        <span class="px-2 py-1 text-xs font-medium rounded-full <?= 
                                            $shipment['status'] === 'open' ? 'bg-green-100 text-green-800' : 
                                            ($shipment['status'] === 'matched' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800')
                                        ?>">
                                            <?= $translations['status_' . $shipment['status']] ?? ucfirst($shipment['status']) ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="mb-4">
                                    <span class="text-sm text-gray-500"><?= $translations['description'] ?? 'Description' ?>:</span>
                                    <p class="text-gray-700 mt-1"><?= htmlspecialchars($shipment['description']) ?></p>
                                </div>
                            </div>
                            
                            <?php if ($shipment['photo']): ?>
                                <div class="<?= $isRTL ? 'mr-4' : 'ml-4' ?>">
                                    <img src="/uploads/<?= htmlspecialchars($shipment['photo']) ?>" 
                                         alt="Shipment photo" 
                                         class="w-20 h-20 object-cover rounded-lg">
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
                            <div class="text-sm text-gray-500">
                                <i class="fas fa-phone <?= $isRTL ? 'ml-1' : 'mr-1' ?>"></i>
                                <?= htmlspecialchars($shipment['sender_phone']) ?>
                            </div>
                            
                            <div class="flex space-x-3 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                                <a href="/shipments/<?= $shipment['id'] ?>" class="text-blue-600 hover:text-blue-500 font-medium">
                                    <?= $translations['view_details'] ?? 'View Details' ?>
                                </a>
                                
                                <?php if ($auth->hasRole('driver') && $shipment['status'] === 'open'): ?>
                                    <button onclick="makeOffer(<?= $shipment['id'] ?>)" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-300">
                                        <?= $translations['make_offer'] ?? 'Make Offer' ?>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Make Offer Modal -->
<div id="offerModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['make_offer'] ?? 'Make Offer' ?></h3>
            
            <form id="offerForm" class="ajax-form" action="/offers/create" method="POST">
                <input type="hidden" id="offerShipmentId" name="shipment_id">
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2"><?= $translations['total_price'] ?? 'Total Price (MAD)' ?></label>
                    <input type="number" name="price_total" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="<?= $translations['enter_price'] ?? 'Enter your offer price' ?>">
                </div>
                
                <div class="flex justify-center space-x-4 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                    <button type="button" onclick="closeOfferModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        <?= $translations['cancel'] ?? 'Cancel' ?>
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        <?= $translations['send_offer'] ?? 'Send Offer' ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function makeOffer(shipmentId) {
    document.getElementById('offerShipmentId').value = shipmentId;
    document.getElementById('offerModal').classList.remove('hidden');
}

function closeOfferModal() {
    document.getElementById('offerModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('offerModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeOfferModal();
    }
});
</script>

<?php include __DIR__ . '/../layout/footer.php'; ?>
