<?php
class ShipmentController extends Controller {
    
    public function index() {
        $this->requireAuth();
        
        $userId = $this->auth->id();
        $userRole = $_SESSION['user']['role'];
        
        // Search parameters
        $fromCity = $_GET['from_city'] ?? '';
        $toCity = $_GET['to_city'] ?? '';
        $date = $_GET['date'] ?? '';
        $maxWeight = $_GET['max_weight'] ?? '';
        
        $sql = "SELECT s.*, u.name as sender_name, u.phone as sender_phone 
                FROM shipments s 
                JOIN users u ON s.user_id = u.id 
                WHERE s.status = 'open'";
        $params = [];
        
        // If sender, show only their shipments
        if ($userRole === 'sender') {
            $sql .= " AND s.user_id = ?";
            $params[] = $userId;
        }
        
        // Apply search filters
        if ($fromCity) {
            $sql .= " AND s.from_city LIKE ?";
            $params[] = "%{$fromCity}%";
        }
        
        if ($toCity) {
            $sql .= " AND s.to_city LIKE ?";
            $params[] = "%{$toCity}%";
        }
        
        if ($date) {
            $sql .= " AND s.ready_date >= ?";
            $params[] = $date;
        }
        
        if ($maxWeight) {
            $sql .= " AND s.weight_kg <= ?";
            $params[] = $maxWeight;
        }
        
        $sql .= " ORDER BY s.ready_date ASC, s.created_at DESC";
        
        $stmt = $this->db->query($sql, $params);
        $shipments = $stmt->fetchAll();
        
        $this->view('shipments/index', compact('shipments'));
    }
    
    public function create() {
        $this->requireAuth(['sender']);
        $this->view('shipments/create');
    }
    
    public function store() {
        $this->requireAuth(['sender']);
        $this->validateCsrf();
        
        $data = [
            'from_city' => $_POST['from_city'] ?? '',
            'to_city' => $_POST['to_city'] ?? '',
            'ready_date' => $_POST['ready_date'] ?? '',
            'weight_kg' => $_POST['weight_kg'] ?? 0,
            'description' => $_POST['description'] ?? '',
            'fragile' => isset($_POST['fragile']) ? 1 : 0
        ];
        
        // Validation
        $errors = [];
        
        if (empty($data['from_city'])) {
            $errors['from_city'] = 'From city is required';
        }
        
        if (empty($data['to_city'])) {
            $errors['to_city'] = 'To city is required';
        }
        
        if (empty($data['ready_date'])) {
            $errors['ready_date'] = 'Ready date is required';
        } elseif (strtotime($data['ready_date']) < strtotime('today')) {
            $errors['ready_date'] = 'Ready date cannot be in the past';
        }
        
        if (empty($data['weight_kg']) || $data['weight_kg'] <= 0) {
            $errors['weight_kg'] = 'Weight must be greater than 0';
        }
        
        if (empty($data['description'])) {
            $errors['description'] = 'Description is required';
        }
        
        // Handle photo upload
        $photo = null;
        if (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) {
            $photo = $this->uploadFile($_FILES['photo'], 'shipment_');
            if (!$photo) {
                $errors['photo'] = 'Invalid photo file';
            }
        }
        
        if (!empty($errors)) {
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'errors' => $errors]);
            } else {
                $this->view('shipments/create', ['errors' => $errors, 'data' => $data]);
                return;
            }
        }
        
        // Insert shipment
        $stmt = $this->db->query(
            "INSERT INTO shipments (user_id, from_city, to_city, ready_date, weight_kg, description, fragile, photo, created_at, updated_at) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())",
            [
                $this->auth->id(),
                $data['from_city'],
                $data['to_city'],
                $data['ready_date'],
                $data['weight_kg'],
                $data['description'],
                $data['fragile'],
                $photo
            ]
        );
        
        if ($stmt) {
            $message = 'Shipment created successfully';
            if ($this->isAjaxRequest()) {
                $this->json(['success' => true, 'message' => $message, 'redirect' => '/shipments']);
            } else {
                $this->redirect('/shipments');
            }
        } else {
            $message = 'Failed to create shipment';
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => $message]);
            } else {
                $this->view('shipments/create', ['error' => $message, 'data' => $data]);
            }
        }
    }
    
    public function show($id) {
        $this->requireAuth();
        
        $stmt = $this->db->query(
            "SELECT s.*, u.name as sender_name, u.phone as sender_phone, u.email as sender_email
             FROM shipments s 
             JOIN users u ON s.user_id = u.id 
             WHERE s.id = ?",
            [$id]
        );
        
        $shipment = $stmt->fetch();
        
        if (!$shipment) {
            $this->redirect('/shipments');
        }
        
        // Get related offers
        $offersStmt = $this->db->query(
            "SELECT o.*, t.from_city as trip_from, t.to_city as trip_to, t.trip_date,
                    u.name as driver_name, u.phone as driver_phone
             FROM offers o
             JOIN trips t ON o.trip_id = t.id
             JOIN users u ON o.proposer_id = u.id
             WHERE o.shipment_id = ?
             ORDER BY o.created_at DESC",
            [$id]
        );
        
        $offers = $offersStmt->fetchAll();
        
        $this->view('shipments/show', compact('shipment', 'offers'));
    }
    
    public function edit($id) {
        $this->requireAuth(['sender']);
        
        $stmt = $this->db->query(
            "SELECT * FROM shipments WHERE id = ? AND user_id = ?",
            [$id, $this->auth->id()]
        );
        
        $shipment = $stmt->fetch();
        
        if (!$shipment) {
            $this->redirect('/shipments');
        }
        
        $this->view('shipments/edit', compact('shipment'));
    }
    
    public function update($id) {
        $this->requireAuth(['sender']);
        $this->validateCsrf();
        
        // Check ownership
        $stmt = $this->db->query(
            "SELECT * FROM shipments WHERE id = ? AND user_id = ?",
            [$id, $this->auth->id()]
        );
        
        $shipment = $stmt->fetch();
        
        if (!$shipment) {
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => 'Shipment not found'], 404);
            } else {
                $this->redirect('/shipments');
            }
        }
        
        $data = [
            'from_city' => $_POST['from_city'] ?? '',
            'to_city' => $_POST['to_city'] ?? '',
            'ready_date' => $_POST['ready_date'] ?? '',
            'weight_kg' => $_POST['weight_kg'] ?? 0,
            'description' => $_POST['description'] ?? '',
            'fragile' => isset($_POST['fragile']) ? 1 : 0
        ];
        
        // Validation (same as store method)
        $errors = [];
        
        if (empty($data['from_city'])) {
            $errors['from_city'] = 'From city is required';
        }
        
        if (empty($data['to_city'])) {
            $errors['to_city'] = 'To city is required';
        }
        
        if (empty($data['ready_date'])) {
            $errors['ready_date'] = 'Ready date is required';
        }
        
        if (empty($data['weight_kg']) || $data['weight_kg'] <= 0) {
            $errors['weight_kg'] = 'Weight must be greater than 0';
        }
        
        if (empty($data['description'])) {
            $errors['description'] = 'Description is required';
        }
        
        // Handle photo upload
        $photo = $shipment['photo']; // Keep existing photo
        if (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) {
            $newPhoto = $this->uploadFile($_FILES['photo'], 'shipment_');
            if ($newPhoto) {
                // Delete old photo
                if ($photo) {
                    $oldPhotoPath = Config::get('upload.path') . $photo;
                    if (file_exists($oldPhotoPath)) {
                        unlink($oldPhotoPath);
                    }
                }
                $photo = $newPhoto;
            } else {
                $errors['photo'] = 'Invalid photo file';
            }
        }
        
        if (!empty($errors)) {
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'errors' => $errors]);
            } else {
                $this->view('shipments/edit', ['errors' => $errors, 'shipment' => array_merge($shipment, $data)]);
                return;
            }
        }
        
        // Update shipment
        $stmt = $this->db->query(
            "UPDATE shipments SET from_city = ?, to_city = ?, ready_date = ?, weight_kg = ?, 
             description = ?, fragile = ?, photo = ?, updated_at = NOW()
             WHERE id = ? AND user_id = ?",
            [
                $data['from_city'],
                $data['to_city'],
                $data['ready_date'],
                $data['weight_kg'],
                $data['description'],
                $data['fragile'],
                $photo,
                $id,
                $this->auth->id()
            ]
        );
        
        if ($stmt) {
            $message = 'Shipment updated successfully';
            if ($this->isAjaxRequest()) {
                $this->json(['success' => true, 'message' => $message, 'redirect' => "/shipments/{$id}"]);
            } else {
                $this->redirect("/shipments/{$id}");
            }
        } else {
            $message = 'Failed to update shipment';
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => $message]);
            } else {
                $this->view('shipments/edit', ['error' => $message, 'shipment' => array_merge($shipment, $data)]);
            }
        }
    }
    
    public function delete($id) {
        $this->requireAuth(['sender']);
        $this->validateCsrf();
        
        // Check ownership
        $stmt = $this->db->query(
            "SELECT * FROM shipments WHERE id = ? AND user_id = ?",
            [$id, $this->auth->id()]
        );
        
        $shipment = $stmt->fetch();
        
        if (!$shipment) {
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => 'Shipment not found'], 404);
            } else {
                $this->redirect('/shipments');
            }
        }
        
        // Update status to cancelled instead of deleting
        $stmt = $this->db->query(
            "UPDATE shipments SET status = 'cancelled', updated_at = NOW() WHERE id = ?",
            [$id]
        );
        
        if ($stmt) {
            // Delete photo if exists
            if ($shipment['photo']) {
                $photoPath = Config::get('upload.path') . $shipment['photo'];
                if (file_exists($photoPath)) {
                    unlink($photoPath);
                }
            }
            
            $message = 'Shipment cancelled successfully';
            if ($this->isAjaxRequest()) {
                $this->json(['success' => true, 'message' => $message, 'redirect' => '/shipments']);
            } else {
                $this->redirect('/shipments');
            }
        } else {
            $message = 'Failed to cancel shipment';
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => $message]);
            } else {
                $this->redirect("/shipments/{$id}");
            }
        }
    }
}
?>
