<!DOCTYPE html>
<html lang="<?= $lang ?>" dir="<?= $isRTL ? 'rtl' : 'ltr' ?>" data-user-role="<?= $_SESSION['user']['role'] ?? '' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?= (new Auth())->generateCsrf() ?>">
    <title><?= $title ?? 'Tawssil Pro' ?></title>
    
    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Noto Sans Arabic', 'Arial', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    
    <!-- Leaflet CSS for maps -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: <?= $isRTL ? "'Noto Sans Arabic', Arial, sans-serif" : "ui-sans-serif, system-ui, sans-serif" ?>;
        }
        
        .driver-marker {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .driver-icon {
            background: none !important;
            border: none !important;
        }
        
        /* RTL adjustments */
        [dir="rtl"] .text-left {
            text-align: right;
        }
        
        [dir="rtl"] .text-right {
            text-align: left;
        }
        
        [dir="rtl"] .ml-auto {
            margin-left: 0;
            margin-right: auto;
        }
        
        [dir="rtl"] .mr-auto {
            margin-right: 0;
            margin-left: auto;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen" data-user-role="<?= $_SESSION['user']['role'] ?? '' ?>">
