<?php 
$title = $translations['offer_details'] ?? 'Offer Details - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                    <?= $translations['offer_details'] ?? 'Offer Details' ?>
                </h1>
                <p class="text-gray-600">
                    <?php if ($offer['trip_id']): ?>
                        <?= $translations['offer_for_trip'] ?? 'Offer for Trip' ?>: <?= htmlspecialchars($offer['trip_from']) ?> → <?= htmlspecialchars($offer['trip_to']) ?>
                    <?php else: ?>
                        <?= $translations['offer_for_shipment'] ?? 'Offer for Shipment' ?>: <?= htmlspecialchars($offer['shipment_from']) ?> → <?= htmlspecialchars($offer['shipment_to']) ?>
                    <?php endif; ?>
                </p>
            </div>
            
            <span class="px-3 py-1 text-sm font-medium rounded-full <?= 
                $offer['status'] === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                ($offer['status'] === 'accepted' ? 'bg-green-100 text-green-800' : 
                ($offer['status'] === 'rejected' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'))
            ?>">
                <?= $translations['status_' . $offer['status']] ?? ucfirst($offer['status']) ?>
            </span>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Offer Information -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900"><?= $translations['offer_information'] ?? 'Offer Information' ?></h2>
                </div>
                
                <div class="px-6 py-6">
                    <?php if ($offer['trip_id']): ?>
                        <!-- Trip Offer Details -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <h3 class="text-sm font-medium text-gray-500 mb-1"><?= $translations['trip_details'] ?? 'Trip Details' ?></h3>
                                <div class="space-y-2">
                                    <div class="flex items-center">
                                        <i class="fas fa-route text-blue-600 w-5"></i>
                                        <span class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> text-gray-900">
                                            <?= htmlspecialchars($offer['trip_from']) ?> → <?= htmlspecialchars($offer['trip_to']) ?>
                                        </span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar text-blue-600 w-5"></i>
                                        <span class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> text-gray-900">
                                            <?= date('M d, Y', strtotime($offer['trip_date'])) ?>
                                        </span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-weight-hanging text-blue-600 w-5"></i>
                                        <span class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> text-gray-900">
                                            <?= $offer['capacity_kg'] ?> kg <?= $translations['capacity'] ?? 'capacity' ?>
                                        </span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-money-bill-wave text-blue-600 w-5"></i>
                                        <span class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> text-gray-900">
                                            <?= $offer['price_per_kg'] ?> MAD/kg
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h3 class="text-sm font-medium text-gray-500 mb-1"><?= $translations['driver_details'] ?? 'Driver Details' ?></h3>
                                <div class="space-y-2">
                                    <div class="flex items-center">
                                        <i class="fas fa-user text-green-600 w-5"></i>
                                        <span class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> text-gray-900"><?= htmlspecialchars($offer['receiver_name']) ?></span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-phone text-green-600 w-5"></i>
                                        <span class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> text-gray-900"><?= htmlspecialchars($offer['receiver_phone']) ?></span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-envelope text-green-600 w-5"></i>
                                        <span class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> text-gray-900"><?= htmlspecialchars($offer['receiver_email']) ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- Shipment Offer Details -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <h3 class="text-sm font-medium text-gray-500 mb-1"><?= $translations['shipment_details'] ?? 'Shipment Details' ?></h3>
                                <div class="space-y-2">
                                    <div class="flex items-center">
                                        <i class="fas fa-box text-green-600 w-5"></i>
                                        <span class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> text-gray-900">
                                            <?= htmlspecialchars($offer['shipment_from']) ?> → <?= htmlspecialchars($offer['shipment_to']) ?>
                                        </span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar text-green-600 w-5"></i>
                                        <span class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> text-gray-900">
                                            <?= date('M d, Y', strtotime($offer['ready_date'])) ?>
                                        </span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-weight-hanging text-green-600 w-5"></i>
                                        <span class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> text-gray-900">
                                            <?= $offer['weight_kg'] ?> kg
                                        </span>
                                    </div>
                                    <?php if ($offer['fragile']): ?>
                                        <div class="flex items-center">
                                            <i class="fas fa-exclamation-triangle text-orange-600 w-5"></i>
                                            <span class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> text-orange-600 font-medium">
                                                <?= $translations['fragile'] ?? 'Fragile' ?>
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div>
                                <h3 class="text-sm font-medium text-gray-500 mb-1"><?= $translations['sender_details'] ?? 'Sender Details' ?></h3>
                                <div class="space-y-2">
                                    <div class="flex items-center">
                                        <i class="fas fa-user text-blue-600 w-5"></i>
                                        <span class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> text-gray-900"><?= htmlspecialchars($offer['receiver_name']) ?></span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-phone text-blue-600 w-5"></i>
                                        <span class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> text-gray-900"><?= htmlspecialchars($offer['receiver_phone']) ?></span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-envelope text-blue-600 w-5"></i>
                                        <span class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> text-gray-900"><?= htmlspecialchars($offer['receiver_email']) ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <?php if ($offer['description']): ?>
                            <div class="mb-6">
                                <h3 class="text-sm font-medium text-gray-500 mb-2"><?= $translations['description'] ?? 'Description' ?></h3>
                                <p class="text-gray-900"><?= nl2br(htmlspecialchars($offer['description'])) ?></p>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                    
                    <!-- Offer Price -->
                    <?php if ($offer['price_total']): ?>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-medium text-gray-900"><?= $translations['offered_price'] ?? 'Offered Price' ?>:</span>
                                <span class="text-2xl font-bold text-green-600"><?= $offer['price_total'] ?> MAD</span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Messages Section -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h2 class="text-lg font-semibold text-gray-900">
                            <?= $translations['messages'] ?? 'Messages' ?> (<?= count($messages) ?>)
                        </h2>
                        <a href="/messages/thread/<?= $offer['id'] ?>" class="text-blue-600 hover:text-blue-500 text-sm font-medium">
                            <?= $translations['view_conversation'] ?? 'View Conversation' ?>
                        </a>
                    </div>
                </div>
                
                <div class="px-6 py-6">
                    <?php if (empty($messages)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-comments text-4xl text-gray-300 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2"><?= $translations['no_messages_yet'] ?? 'No messages yet' ?></h3>
                            <p class="text-gray-600 mb-4"><?= $translations['start_conversation'] ?? 'Start a conversation to discuss the details of this offer.' ?></p>
                            <a href="/messages/thread/<?= $offer['id'] ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                                <?= $translations['send_message'] ?? 'Send Message' ?>
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="space-y-4 max-h-96 overflow-y-auto">
                            <?php foreach (array_slice($messages, -5) as $message): ?>
                                <div class="flex <?= $message['sender_id'] == $auth->id() ? 'justify-end' : 'justify-start' ?>">
                                    <div class="max-w-xs lg:max-w-md">
                                        <div class="<?= $message['sender_id'] == $auth->id() ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-900' ?> rounded-lg px-4 py-2">
                                            <p class="text-sm"><?= nl2br(htmlspecialchars($message['body'])) ?></p>
                                            <?php if ($message['attachment']): ?>
                                                <a href="/uploads/<?= htmlspecialchars($message['attachment']) ?>" target="_blank" class="<?= $message['sender_id'] == $auth->id() ? 'text-blue-200' : 'text-blue-600' ?> text-xs hover:underline">
                                                    <i class="fas fa-paperclip <?= $isRTL ? 'ml-1' : 'mr-1' ?>"></i>
                                                    <?= $translations['attachment'] ?? 'Attachment' ?>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                        <div class="<?= $message['sender_id'] == $auth->id() ? 'text-right' : 'text-left' ?> mt-1">
                                            <span class="text-xs text-gray-500">
                                                <?= htmlspecialchars($message['sender_name']) ?> • 
                                                <?= date('M d, H:i', strtotime($message['created_at'])) ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <?php if (count($messages) > 5): ?>
                            <div class="mt-4 text-center">
                                <a href="/messages/thread/<?= $offer['id'] ?>" class="text-blue-600 hover:text-blue-500 text-sm">
                                    <?= $translations['view_all_messages'] ?? 'View all messages' ?> (<?= count($messages) ?>)
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Actions Card -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900"><?= $translations['actions'] ?? 'Actions' ?></h2>
                </div>
                
                <div class="px-6 py-6 space-y-4">
                    <?php if ($offer['status'] === 'pending'): ?>
                        <?php if ($auth->id() == $offer['receiver_id']): ?>
                            <!-- Receiver actions -->
                            <form class="ajax-form" action="/offers/<?= $offer['id'] ?>/accept" method="POST">
                                <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md font-medium">
                                    <i class="fas fa-check <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                                    <?= $translations['accept_offer'] ?? 'Accept Offer' ?>
                                </button>
                            </form>
                            
                            <form class="ajax-form" action="/offers/<?= $offer['id'] ?>/reject" method="POST">
                                <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md font-medium">
                                    <i class="fas fa-times <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                                    <?= $translations['reject_offer'] ?? 'Reject Offer' ?>
                                </button>
                            </form>
                        <?php elseif ($auth->id() == $offer['proposer_id']): ?>
                            <!-- Proposer actions -->
                            <form class="ajax-form" action="/offers/<?= $offer['id'] ?>/cancel" method="POST" 
                                  onsubmit="return confirm('<?= $translations['confirm_cancel_offer'] ?? 'Are you sure you want to cancel this offer?' ?>')">
                                <button type="submit" class="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md font-medium">
                                    <i class="fas fa-ban <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                                    <?= $translations['cancel_offer'] ?? 'Cancel Offer' ?>
                                </button>
                            </form>
                        <?php endif; ?>
                    <?php elseif ($offer['status'] === 'accepted' && $auth->id() == $offer['proposer_id']): ?>
                        <!-- Cancel accepted offer -->
                        <form class="ajax-form" action="/offers/<?= $offer['id'] ?>/cancel" method="POST" 
                              onsubmit="return confirm('<?= $translations['confirm_cancel_accepted'] ?? 'Are you sure you want to cancel this accepted offer?' ?>')">
                            <button type="submit" class="w-full bg-orange-600 hover:bg-orange-700 text-white py-2 px-4 rounded-md font-medium">
                                <i class="fas fa-ban <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                                <?= $translations['cancel_accepted_offer'] ?? 'Cancel Accepted Offer' ?>
                            </button>
                        </form>
                    <?php endif; ?>
                    
                    <!-- Message action -->
                    <a href="/messages/thread/<?= $offer['id'] ?>" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md font-medium text-center inline-block">
                        <i class="fas fa-comment <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                        <?= $translations['send_message'] ?? 'Send Message' ?>
                    </a>
                    
                    <!-- View original -->
                    <?php if ($offer['trip_id']): ?>
                        <a href="/trips/<?= $offer['trip_id'] ?>" class="w-full border border-gray-300 text-gray-700 hover:bg-gray-50 py-2 px-4 rounded-md font-medium text-center inline-block">
                            <i class="fas fa-route <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                            <?= $translations['view_trip'] ?? 'View Trip' ?>
                        </a>
                    <?php else: ?>
                        <a href="/shipments/<?= $offer['shipment_id'] ?>" class="w-full border border-gray-300 text-gray-700 hover:bg-gray-50 py-2 px-4 rounded-md font-medium text-center inline-block">
                            <i class="fas fa-box <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                            <?= $translations['view_shipment'] ?? 'View Shipment' ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Offer Summary -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900"><?= $translations['offer_summary'] ?? 'Offer Summary' ?></h2>
                </div>
                
                <div class="px-6 py-6 space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-500"><?= $translations['created'] ?? 'Created' ?>:</span>
                        <span class="font-medium"><?= date('M d, Y', strtotime($offer['created_at'])) ?></span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-500"><?= $translations['updated'] ?? 'Updated' ?>:</span>
                        <span class="font-medium"><?= date('M d, Y', strtotime($offer['updated_at'])) ?></span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-500"><?= $translations['proposer'] ?? 'Proposer' ?>:</span>
                        <span class="font-medium"><?= htmlspecialchars($offer['proposer_name']) ?></span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-500"><?= $translations['receiver'] ?? 'Receiver' ?>:</span>
                        <span class="font-medium"><?= htmlspecialchars($offer['receiver_name']) ?></span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-500"><?= $translations['messages'] ?? 'Messages' ?>:</span>
                        <span class="font-medium"><?= count($messages) ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../layout/footer.php'; ?>
