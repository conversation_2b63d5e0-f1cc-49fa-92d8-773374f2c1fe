<?php
/**
 * <PERSON><PERSON>r Handler for Tawssil Pro
 * Handles PHP errors, exceptions, and provides debugging information
 */
class ErrorHandler {
    
    /**
     * Register error and exception handlers
     */
    public static function register() {
        // Set error reporting based on debug mode
        if (Config::get('app.debug')) {
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
            ini_set('display_startup_errors', 1);
        } else {
            error_reporting(E_ALL & ~E_NOTICE & ~E_STRICT & ~E_DEPRECATED);
            ini_set('display_errors', 0);
            ini_set('display_startup_errors', 0);
        }
        
        // Register custom error handler
        set_error_handler([self::class, 'handleError']);
        
        // Register exception handler
        set_exception_handler([self::class, 'handleException']);
        
        // Register shutdown function for fatal errors
        register_shutdown_function([self::class, 'handleShutdown']);
    }
    
    /**
     * Handle PHP errors
     */
    public static function handleError($severity, $message, $file, $line) {
        // Don't handle errors that are suppressed with @
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $errorType = self::getErrorType($severity);
        
        // Log the error
        self::logError($errorType, $message, $file, $line);
        
        // Display error if in debug mode
        if (Config::get('app.debug')) {
            self::displayError($errorType, $message, $file, $line);
        }
        
        // Don't execute PHP internal error handler
        return true;
    }
    
    /**
     * Handle uncaught exceptions
     */
    public static function handleException($exception) {
        $message = $exception->getMessage();
        $file = $exception->getFile();
        $line = $exception->getLine();
        $trace = $exception->getTraceAsString();
        
        // Log the exception
        self::logError('Exception', $message, $file, $line, $trace);
        
        // Display error page
        if (Config::get('app.debug')) {
            self::displayException($exception);
        } else {
            self::displayGenericError();
        }
    }
    
    /**
     * Handle fatal errors during shutdown
     */
    public static function handleShutdown() {
        $error = error_get_last();
        
        if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            self::handleError($error['type'], $error['message'], $error['file'], $error['line']);
        }
    }
    
    /**
     * Get error type string
     */
    private static function getErrorType($severity) {
        $errorTypes = [
            E_ERROR => 'Fatal Error',
            E_WARNING => 'Warning',
            E_PARSE => 'Parse Error',
            E_NOTICE => 'Notice',
            E_CORE_ERROR => 'Core Error',
            E_CORE_WARNING => 'Core Warning',
            E_COMPILE_ERROR => 'Compile Error',
            E_COMPILE_WARNING => 'Compile Warning',
            E_USER_ERROR => 'User Error',
            E_USER_WARNING => 'User Warning',
            E_USER_NOTICE => 'User Notice',
            E_STRICT => 'Strict Standards',
            E_RECOVERABLE_ERROR => 'Recoverable Error',
            E_DEPRECATED => 'Deprecated',
            E_USER_DEPRECATED => 'User Deprecated'
        ];
        
        return $errorTypes[$severity] ?? 'Unknown Error';
    }
    
    /**
     * Log error to file
     */
    private static function logError($type, $message, $file, $line, $trace = null) {
        $logDir = __DIR__ . '/../../logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logFile = $logDir . '/error.log';
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$type}: {$message} in {$file} on line {$line}";
        
        if ($trace) {
            $logMessage .= "\nStack trace:\n{$trace}";
        }
        
        $logMessage .= "\n" . str_repeat('-', 80) . "\n";
        
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Display error for debugging
     */
    private static function displayError($type, $message, $file, $line) {
        if (!headers_sent()) {
            http_response_code(500);
        }
        
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
        echo "<h4>{$type}</h4>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($message) . "</p>";
        echo "<p><strong>File:</strong> " . htmlspecialchars($file) . "</p>";
        echo "<p><strong>Line:</strong> {$line}</p>";
        echo "</div>";
    }
    
    /**
     * Display exception with full details
     */
    private static function displayException($exception) {
        if (!headers_sent()) {
            http_response_code(500);
        }
        
        echo "<!DOCTYPE html>";
        echo "<html><head><title>Application Error</title>";
        echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .error{background:#f8d7da;color:#721c24;padding:20px;border:1px solid #f5c6cb;border-radius:4px;} .trace{background:#f8f9fa;padding:15px;margin-top:15px;border:1px solid #dee2e6;border-radius:4px;overflow-x:auto;} pre{margin:0;}</style>";
        echo "</head><body>";
        echo "<div class='error'>";
        echo "<h2>Uncaught Exception</h2>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($exception->getMessage()) . "</p>";
        echo "<p><strong>File:</strong> " . htmlspecialchars($exception->getFile()) . "</p>";
        echo "<p><strong>Line:</strong> " . $exception->getLine() . "</p>";
        echo "<div class='trace'>";
        echo "<h4>Stack Trace:</h4>";
        echo "<pre>" . htmlspecialchars($exception->getTraceAsString()) . "</pre>";
        echo "</div>";
        echo "</div>";
        echo "</body></html>";
    }
    
    /**
     * Display generic error page for production
     */
    private static function displayGenericError() {
        if (!headers_sent()) {
            http_response_code(500);
        }
        
        echo "<!DOCTYPE html>";
        echo "<html><head><title>Server Error</title>";
        echo "<style>body{font-family:Arial,sans-serif;text-align:center;margin-top:100px;} .error{color:#721c24;}</style>";
        echo "</head><body>";
        echo "<div class='error'>";
        echo "<h1>500 - Internal Server Error</h1>";
        echo "<p>Something went wrong. Please try again later.</p>";
        echo "</div>";
        echo "</body></html>";
    }
}
?>
