<?php
class Shipment {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function findById($id) {
        $stmt = $this->db->query(
            "SELECT s.*, u.name as sender_name, u.phone as sender_phone, u.email as sender_email
             FROM shipments s 
             JOIN users u ON s.user_id = u.id 
             WHERE s.id = ?",
            [$id]
        );
        return $stmt->fetch();
    }
    
    public function create($data) {
        $stmt = $this->db->query(
            "INSERT INTO shipments (user_id, from_city, to_city, ready_date, weight_kg, description, fragile, photo, created_at, updated_at) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())",
            [
                $data['user_id'],
                $data['from_city'],
                $data['to_city'],
                $data['ready_date'],
                $data['weight_kg'],
                $data['description'],
                $data['fragile'],
                $data['photo'] ?? null
            ]
        );
        
        return $stmt ? $this->db->lastInsertId() : false;
    }
    
    public function update($id, $data) {
        $stmt = $this->db->query(
            "UPDATE shipments SET from_city = ?, to_city = ?, ready_date = ?, weight_kg = ?, 
             description = ?, fragile = ?, photo = ?, updated_at = NOW()
             WHERE id = ?",
            [
                $data['from_city'],
                $data['to_city'],
                $data['ready_date'],
                $data['weight_kg'],
                $data['description'],
                $data['fragile'],
                $data['photo'],
                $id
            ]
        );
        
        return $stmt !== false;
    }
    
    public function delete($id) {
        $stmt = $this->db->query(
            "UPDATE shipments SET status = 'cancelled', updated_at = NOW() WHERE id = ?",
            [$id]
        );
        
        return $stmt !== false;
    }
    
    public function search($filters = []) {
        $sql = "SELECT s.*, u.name as sender_name, u.phone as sender_phone 
                FROM shipments s 
                JOIN users u ON s.user_id = u.id 
                WHERE s.status = 'open'";
        $params = [];
        
        if (!empty($filters['from_city'])) {
            $sql .= " AND s.from_city LIKE ?";
            $params[] = "%{$filters['from_city']}%";
        }
        
        if (!empty($filters['to_city'])) {
            $sql .= " AND s.to_city LIKE ?";
            $params[] = "%{$filters['to_city']}%";
        }
        
        if (!empty($filters['date'])) {
            $sql .= " AND s.ready_date >= ?";
            $params[] = $filters['date'];
        }
        
        if (!empty($filters['max_weight'])) {
            $sql .= " AND s.weight_kg <= ?";
            $params[] = $filters['max_weight'];
        }
        
        $sql .= " ORDER BY s.ready_date ASC, s.created_at DESC";
        
        $stmt = $this->db->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    public function getByUser($userId) {
        $stmt = $this->db->query(
            "SELECT * FROM shipments WHERE user_id = ? ORDER BY created_at DESC",
            [$userId]
        );
        return $stmt->fetchAll();
    }
    
    public function getAll($filters = []) {
        $sql = "SELECT s.*, u.name as sender_name, u.email as sender_email 
                FROM shipments s 
                JOIN users u ON s.user_id = u.id";
        $params = [];
        
        if (isset($filters['status']) && $filters['status'] !== 'all') {
            $sql .= " WHERE s.status = ?";
            $params[] = $filters['status'];
        }
        
        $sql .= " ORDER BY s.created_at DESC";
        
        $stmt = $this->db->query($sql, $params);
        return $stmt->fetchAll();
    }
}
?>
