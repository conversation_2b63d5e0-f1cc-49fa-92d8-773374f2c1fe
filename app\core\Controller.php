<?php
// Base controller class
class Controller {
    protected $db;
    protected $auth;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->auth = new Auth();
    }
    
    public function view($viewPath, $data = []) {
        // Set up language and translations first
        $lang = $_SESSION['language'] ?? Config::get('app.default_language');
        $translations = $this->loadTranslations($lang);
        $isRTL = $lang === 'ar';

        // Make auth available to views
        $auth = $this->auth;

        // Extract data to variables (this should not overwrite our system variables)
        extract($data);
        
        // Include the view file
        $viewFile = __DIR__ . '/../views/' . $viewPath . '.php';
        
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            throw new Exception("View not found: {$viewPath}");
        }
    }
    
    protected function json($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    
    protected function redirect($url, $statusCode = 302) {
        http_response_code($statusCode);
        header("Location: {$url}");
        exit;
    }
    
    protected function validateCsrf() {
        $token = $_POST['csrf_token'] ?? $_GET['csrf_token'] ?? null;
        if (!$this->auth->validateCsrf($token)) {
            $this->json(['success' => false, 'message' => 'Invalid CSRF token'], 403);
        }
    }
    
    protected function requireAuth($roles = []) {
        if (!$this->auth->check()) {
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => 'Authentication required'], 401);
            } else {
                $this->redirect(url('/login'));
            }
        }
        
        if (!empty($roles) && !in_array($_SESSION['user']['role'], $roles)) {
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => 'Access denied'], 403);
            } else {
                $this->redirect(url('/dashboard'));
            }
        }
    }
    
    protected function isAjaxRequest() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    protected function uploadFile($file, $prefix = '') {
        if (!isset($file['tmp_name']) || $file['error'] !== UPLOAD_ERR_OK) {
            return false;
        }
        
        // Validate file type
        $allowedTypes = Config::get('upload.allowed_types');
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!in_array($mimeType, $allowedTypes)) {
            return false;
        }
        
        // Validate file size
        if ($file['size'] > Config::get('upload.max_size')) {
            return false;
        }
        
        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = $prefix . uniqid() . '.' . $extension;
        $uploadPath = Config::get('upload.path') . $filename;
        
        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            return $filename;
        }
        
        return false;
    }
    
    private function loadTranslations($lang) {
        $translationFile = __DIR__ . "/../lang/{$lang}.php";
        if (file_exists($translationFile)) {
            return include $translationFile;
        }
        return [];
    }
}
?>
