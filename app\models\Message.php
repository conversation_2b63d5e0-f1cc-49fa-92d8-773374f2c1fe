<?php
class Message {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function create($data) {
        // Get offer details to determine receiver
        $stmt = $this->db->query(
            "SELECT proposer_id, receiver_id FROM offers WHERE id = ?",
            [$data['offer_id']]
        );
        $offer = $stmt->fetch();
        
        if (!$offer) {
            return false;
        }
        
        // Determine receiver
        $receiverId = ($offer['proposer_id'] == $data['sender_id']) ? $offer['receiver_id'] : $offer['proposer_id'];
        
        $stmt = $this->db->query(
            "INSERT INTO messages (offer_id, sender_id, receiver_id, body, attachment, created_at) 
             VALUES (?, ?, ?, ?, ?, NOW())",
            [
                $data['offer_id'],
                $data['sender_id'],
                $receiverId,
                $data['body'],
                $data['attachment'] ?? null
            ]
        );
        
        return $stmt ? $this->db->lastInsertId() : false;
    }
    
    public function getByOfferId($offerId, $userId) {
        // First check if user has access to this offer
        $stmt = $this->db->query(
            "SELECT * FROM offers WHERE id = ? AND (proposer_id = ? OR receiver_id = ?)",
            [$offerId, $userId, $userId]
        );
        
        if (!$stmt->fetch()) {
            return [];
        }
        
        $stmt = $this->db->query(
            "SELECT m.*, u.name as sender_name, u.avatar as sender_avatar
             FROM messages m
             JOIN users u ON m.sender_id = u.id
             WHERE m.offer_id = ?
             ORDER BY m.created_at ASC",
            [$offerId]
        );
        
        return $stmt->fetchAll();
    }
    
    public function getConversations($userId) {
        $stmt = $this->db->query(
            "SELECT DISTINCT o.id as offer_id,
                    CASE 
                        WHEN o.trip_id IS NOT NULL THEN CONCAT('Trip: ', t.from_city, ' → ', t.to_city)
                        ELSE CONCAT('Shipment: ', s.from_city, ' → ', s.to_city)
                    END as subject,
                    CASE 
                        WHEN o.proposer_id = ? THEN receiver.name
                        ELSE proposer.name
                    END as other_user_name,
                    (SELECT m2.body FROM messages m2 WHERE m2.offer_id = o.id ORDER BY m2.created_at DESC LIMIT 1) as last_message,
                    (SELECT m2.created_at FROM messages m2 WHERE m2.offer_id = o.id ORDER BY m2.created_at DESC LIMIT 1) as last_message_date
             FROM offers o
             LEFT JOIN trips t ON o.trip_id = t.id
             LEFT JOIN shipments s ON o.shipment_id = s.id
             JOIN users proposer ON o.proposer_id = proposer.id
             JOIN users receiver ON o.receiver_id = receiver.id
             WHERE (o.proposer_id = ? OR o.receiver_id = ?)
             AND EXISTS (SELECT 1 FROM messages m WHERE m.offer_id = o.id)
             ORDER BY last_message_date DESC",
            [$userId, $userId, $userId]
        );
        
        return $stmt->fetchAll();
    }
}
?>
