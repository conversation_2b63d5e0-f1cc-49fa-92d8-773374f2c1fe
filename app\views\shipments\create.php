<?php 
$title = $translations['create_shipment'] ?? 'Create Shipment - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';
?>

<div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900"><?= $translations['create_shipment'] ?? 'Create New Shipment' ?></h1>
        <p class="text-gray-600 mt-2"><?= $translations['create_shipment_subtitle'] ?? 'Post your shipment details to find reliable drivers' ?></p>
    </div>

    <!-- Form -->
    <div class="bg-white shadow rounded-lg">
        <form class="ajax-form" action="/shipments/create" method="POST" enctype="multipart/form-data">
            <div class="px-6 py-6 space-y-6">
                <!-- Route Information -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['route_information'] ?? 'Route Information' ?></h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="from_city" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['from_city'] ?? 'From City' ?> *
                            </label>
                            <input type="text" id="from_city" name="from_city" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                                   placeholder="<?= $translations['enter_pickup_city'] ?? 'Enter pickup city' ?>"
                                   value="<?= htmlspecialchars($data['from_city'] ?? '') ?>">
                        </div>
                        
                        <div>
                            <label for="to_city" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['to_city'] ?? 'To City' ?> *
                            </label>
                            <input type="text" id="to_city" name="to_city" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                                   placeholder="<?= $translations['enter_delivery_city'] ?? 'Enter delivery city' ?>"
                                   value="<?= htmlspecialchars($data['to_city'] ?? '') ?>">
                        </div>
                    </div>
                </div>

                <!-- Shipment Details -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['shipment_details'] ?? 'Shipment Details' ?></h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="ready_date" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['ready_date'] ?? 'Ready Date' ?> *
                            </label>
                            <input type="date" id="ready_date" name="ready_date" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                                   min="<?= date('Y-m-d') ?>"
                                   value="<?= htmlspecialchars($data['ready_date'] ?? '') ?>">
                        </div>
                        
                        <div>
                            <label for="weight_kg" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['weight_kg'] ?? 'Weight (kg)' ?> *
                            </label>
                            <input type="number" id="weight_kg" name="weight_kg" required min="0.1" step="0.1"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                                   placeholder="<?= $translations['enter_weight'] ?? 'e.g., 25.5' ?>"
                                   value="<?= htmlspecialchars($data['weight_kg'] ?? '') ?>">
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        <?= $translations['description'] ?? 'Description' ?> *
                    </label>
                    <textarea id="description" name="description" rows="4" required
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                              placeholder="<?= $translations['shipment_description_placeholder'] ?? 'Describe your shipment (type of goods, dimensions, packaging, etc.)' ?>"><?= htmlspecialchars($data['description'] ?? '') ?></textarea>
                </div>

                <!-- Special Options -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['special_options'] ?? 'Special Options' ?></h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input id="fragile" name="fragile" type="checkbox" 
                                   class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                                   <?= isset($data['fragile']) && $data['fragile'] ? 'checked' : '' ?>>
                            <label for="fragile" class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> block text-sm text-gray-900">
                                <span class="font-medium"><?= $translations['fragile_item'] ?? 'Fragile Item' ?></span>
                                <span class="text-gray-500"> - <?= $translations['fragile_description'] ?? 'Requires special care during transport' ?></span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Photo Upload -->
                <div>
                    <label for="photo" class="block text-sm font-medium text-gray-700 mb-2">
                        <?= $translations['shipment_photo'] ?? 'Shipment Photo' ?>
                    </label>
                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                        <div class="space-y-1 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="flex text-sm text-gray-600">
                                <label for="photo" class="relative cursor-pointer bg-white rounded-md font-medium text-green-600 hover:text-green-500">
                                    <span><?= $translations['upload_photo'] ?? 'Upload a photo' ?></span>
                                    <input id="photo" name="photo" type="file" class="sr-only" accept="image/*" data-preview="photo-preview">
                                </label>
                                <p class="<?= $isRTL ? 'pr-1' : 'pl-1' ?>"><?= $translations['or_drag_drop'] ?? 'or drag and drop' ?></p>
                            </div>
                            <p class="text-xs text-gray-500"><?= $translations['photo_requirements'] ?? 'PNG, JPG, WebP up to 2MB' ?></p>
                        </div>
                    </div>
                    
                    <!-- Photo Preview -->
                    <div class="mt-4 hidden">
                        <img id="photo-preview" src="#" alt="Preview" class="h-32 w-32 object-cover rounded-lg">
                    </div>
                </div>

                <!-- Guidelines -->
                <div class="bg-green-50 border border-green-200 rounded-md p-4">
                    <h4 class="text-sm font-medium text-green-800 mb-2"><?= $translations['important_guidelines'] ?? 'Important Guidelines' ?></h4>
                    <ul class="text-sm text-green-700 space-y-1">
                        <li>• <?= $translations['guideline_describe'] ?? 'Describe your shipment accurately and completely' ?></li>
                        <li>• <?= $translations['guideline_packaging'] ?? 'Ensure proper packaging for safe transport' ?></li>
                        <li>• <?= $translations['guideline_prohibited'] ?? 'Do not ship prohibited or dangerous items' ?></li>
                        <li>• <?= $translations['guideline_verify'] ?? 'Verify driver credentials before handing over goods' ?></li>
                    </ul>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between items-center">
                <a href="/shipments" class="text-gray-600 hover:text-gray-800 font-medium">
                    <i class="fas fa-arrow-<?= $isRTL ? 'right' : 'left' ?> <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                    <?= $translations['back_to_shipments'] ?? 'Back to Shipments' ?>
                </a>
                
                <div class="flex space-x-3 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                    <button type="button" onclick="window.history.back()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium">
                        <?= $translations['cancel'] ?? 'Cancel' ?>
                    </button>
                    <button type="submit" 
                            data-original-text="<?= $translations['create_shipment'] ?? 'Create Shipment' ?>"
                            class="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium transition duration-300">
                        <i class="fas fa-plus <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                        <?= $translations['create_shipment'] ?? 'Create Shipment' ?>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Tips for Success -->
    <div class="mt-8 bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['tips_for_success'] ?? 'Tips for Success' ?></h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="flex items-start">
                <i class="fas fa-lightbulb text-yellow-500 <?= $isRTL ? 'ml-3' : 'mr-3' ?> mt-1"></i>
                <div>
                    <h4 class="font-medium text-gray-900"><?= $translations['detailed_description'] ?? 'Provide Detailed Description' ?></h4>
                    <p class="text-sm text-gray-600"><?= $translations['description_tip'] ?? 'The more details you provide, the better drivers can assess if they can transport your goods safely.' ?></p>
                </div>
            </div>
            
            <div class="flex items-start">
                <i class="fas fa-camera text-blue-500 <?= $isRTL ? 'ml-3' : 'mr-3' ?> mt-1"></i>
                <div>
                    <h4 class="font-medium text-gray-900"><?= $translations['add_photos'] ?? 'Add Photos' ?></h4>
                    <p class="text-sm text-gray-600"><?= $translations['photo_tip'] ?? 'Photos help drivers understand your shipment better and can increase trust.' ?></p>
                </div>
            </div>
            
            <div class="flex items-start">
                <i class="fas fa-clock text-green-500 <?= $isRTL ? 'ml-3' : 'mr-3' ?> mt-1"></i>
                <div>
                    <h4 class="font-medium text-gray-900"><?= $translations['flexible_dates'] ?? 'Be Flexible with Dates' ?></h4>
                    <p class="text-sm text-gray-600"><?= $translations['date_tip'] ?? 'Flexible pickup dates can help you find more available drivers.' ?></p>
                </div>
            </div>
            
            <div class="flex items-start">
                <i class="fas fa-handshake text-purple-500 <?= $isRTL ? 'ml-3' : 'mr-3' ?> mt-1"></i>
                <div>
                    <h4 class="font-medium text-gray-900"><?= $translations['communicate'] ?? 'Communicate Clearly' ?></h4>
                    <p class="text-sm text-gray-600"><?= $translations['communication_tip'] ?? 'Respond promptly to driver inquiries and be clear about pickup/delivery details.' ?></p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../layout/footer.php'; ?>
