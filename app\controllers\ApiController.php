<?php
class ApiController extends Controller {
    
    public function login() {
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        
        if ($this->auth->login($email, $password)) {
            $this->json(['success' => true, 'redirect' => '/dashboard']);
        } else {
            $this->json(['success' => false, 'message' => 'Invalid credentials']);
        }
    }
    
    public function register() {
        $data = [
            'name' => $_POST['name'] ?? '',
            'email' => $_POST['email'] ?? '',
            'phone' => $_POST['phone'] ?? '',
            'password' => $_POST['password'] ?? '',
            'role' => $_POST['role'] ?? 'sender'
        ];
        
        $result = $this->auth->register($data);
        $this->json($result);
    }
    
    public function searchTrips() {
        $fromCity = $_GET['from'] ?? '';
        $toCity = $_GET['to'] ?? '';
        $date = $_GET['date'] ?? '';
        $maxPrice = $_GET['price_max'] ?? '';
        
        $tripModel = new Trip();
        $trips = $tripModel->search([
            'from_city' => $fromCity,
            'to_city' => $toCity,
            'date' => $date,
            'max_price' => $maxPrice
        ]);
        
        $this->json($trips);
    }
    
    public function createTrip() {
        $this->requireAuth(['driver']);
        $this->validateCsrf();
        
        $data = [
            'user_id' => $this->auth->id(),
            'from_city' => $_POST['from_city'] ?? '',
            'to_city' => $_POST['to_city'] ?? '',
            'trip_date' => $_POST['trip_date'] ?? '',
            'capacity_kg' => $_POST['capacity_kg'] ?? 0,
            'price_per_kg' => $_POST['price_per_kg'] ?? 0,
            'notes' => $_POST['notes'] ?? ''
        ];
        
        $tripModel = new Trip();
        $result = $tripModel->create($data);
        
        if ($result) {
            $this->json(['success' => true, 'message' => 'Trip created successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to create trip']);
        }
    }
    
    public function updateTrip($id) {
        $this->requireAuth(['driver']);
        $this->validateCsrf();
        
        $tripModel = new Trip();
        $trip = $tripModel->findById($id);
        
        if (!$trip || $trip['user_id'] != $this->auth->id()) {
            $this->json(['success' => false, 'message' => 'Trip not found'], 404);
        }
        
        $data = [
            'from_city' => $_POST['from_city'] ?? '',
            'to_city' => $_POST['to_city'] ?? '',
            'trip_date' => $_POST['trip_date'] ?? '',
            'capacity_kg' => $_POST['capacity_kg'] ?? 0,
            'price_per_kg' => $_POST['price_per_kg'] ?? 0,
            'notes' => $_POST['notes'] ?? ''
        ];
        
        $result = $tripModel->update($id, $data);
        
        if ($result) {
            $this->json(['success' => true, 'message' => 'Trip updated successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to update trip']);
        }
    }
    
    public function deleteTrip($id) {
        $this->requireAuth(['driver']);
        $this->validateCsrf();
        
        $tripModel = new Trip();
        $trip = $tripModel->findById($id);
        
        if (!$trip || $trip['user_id'] != $this->auth->id()) {
            $this->json(['success' => false, 'message' => 'Trip not found'], 404);
        }
        
        $result = $tripModel->delete($id);
        
        if ($result) {
            $this->json(['success' => true, 'message' => 'Trip deleted successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to delete trip']);
        }
    }
    
    public function searchShipments() {
        $fromCity = $_GET['from'] ?? '';
        $toCity = $_GET['to'] ?? '';
        $date = $_GET['date'] ?? '';
        $maxWeight = $_GET['max_weight'] ?? '';
        
        $shipmentModel = new Shipment();
        $shipments = $shipmentModel->search([
            'from_city' => $fromCity,
            'to_city' => $toCity,
            'date' => $date,
            'max_weight' => $maxWeight
        ]);
        
        $this->json($shipments);
    }
    
    public function createShipment() {
        $this->requireAuth(['sender']);
        $this->validateCsrf();
        
        $data = [
            'user_id' => $this->auth->id(),
            'from_city' => $_POST['from_city'] ?? '',
            'to_city' => $_POST['to_city'] ?? '',
            'ready_date' => $_POST['ready_date'] ?? '',
            'weight_kg' => $_POST['weight_kg'] ?? 0,
            'description' => $_POST['description'] ?? '',
            'fragile' => isset($_POST['fragile']) ? 1 : 0
        ];
        
        $shipmentModel = new Shipment();
        $result = $shipmentModel->create($data);
        
        if ($result) {
            $this->json(['success' => true, 'message' => 'Shipment created successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to create shipment']);
        }
    }
    
    public function createOffer() {
        $this->requireAuth();
        $this->validateCsrf();
        
        $data = [
            'trip_id' => $_POST['trip_id'] ?? null,
            'shipment_id' => $_POST['shipment_id'] ?? null,
            'proposer_id' => $this->auth->id(),
            'price_total' => $_POST['price_total'] ?? null
        ];
        
        $offerModel = new Offer();
        $result = $offerModel->create($data);
        
        if ($result) {
            $this->json(['success' => true, 'message' => 'Offer created successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to create offer']);
        }
    }
    
    public function getMessages($offerId) {
        $this->requireAuth();
        
        $messageModel = new Message();
        $messages = $messageModel->getByOfferId($offerId, $this->auth->id());
        
        $this->json($messages);
    }
    
    public function sendMessage() {
        $this->requireAuth();
        $this->validateCsrf();
        
        $data = [
            'offer_id' => $_POST['offer_id'] ?? '',
            'sender_id' => $this->auth->id(),
            'body' => $_POST['body'] ?? ''
        ];
        
        $messageModel = new Message();
        $result = $messageModel->create($data);
        
        if ($result) {
            $this->json(['success' => true, 'message' => 'Message sent successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to send message']);
        }
    }
    
    public function getUnreadCount() {
        $this->requireAuth();
        
        $notificationModel = new Notification();
        $count = $notificationModel->getUnreadCount($this->auth->id());
        
        $this->json(['count' => $count]);
    }
    
    public function markNotificationsRead() {
        $this->requireAuth();
        $this->validateCsrf();
        
        $notificationModel = new Notification();
        $result = $notificationModel->markAllRead($this->auth->id());
        
        if ($result) {
            $this->json(['success' => true, 'message' => 'Notifications marked as read']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to mark notifications as read']);
        }
    }
    
    public function updateLocation() {
        $this->requireAuth(['driver']);
        $this->validateCsrf();
        
        $latitude = $_POST['latitude'] ?? '';
        $longitude = $_POST['longitude'] ?? '';
        
        if (empty($latitude) || empty($longitude)) {
            $this->json(['success' => false, 'message' => 'Latitude and longitude are required']);
        }
        
        $userModel = new User();
        $result = $userModel->updateLocation($this->auth->id(), $latitude, $longitude);
        
        if ($result) {
            $this->json(['success' => true, 'message' => 'Location updated successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to update location']);
        }
    }
    
    public function getDriverLocations() {
        $userModel = new User();
        $drivers = $userModel->getActiveDrivers();
        
        $this->json($drivers);
    }
}
?>
