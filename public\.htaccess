# Simple .htaccess for basic routing
DirectoryIndex index.php

# Optional: Enable rewriting for clean URLs
# RewriteEngine On
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteRule . index.php [L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Cache static files
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
    Header set Cache-Control "public, max-age=2592000"
</FilesMatch>

# Deny access to sensitive files
<FilesMatch "\.(env|sql|md|txt)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Deny access to app directory
RedirectMatch 404 ^/app/
RedirectMatch 404 ^/scripts/
