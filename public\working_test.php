<!DOCTYPE html>
<html>
<head>
    <title>Tawssil Pro - Working Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        a { color: blue; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h1>🚛 Tawssil Pro - Basic System Test</h1>
    
    <h2>✅ System Status</h2>
    <p class="success">✓ PHP is working</p>
    <p class="success">✓ Basic routing implemented</p>
    <p class="success">✓ No complex dependencies</p>
    
    <h2>🔗 Test Links</h2>
    <ul>
        <li><a href="index.php">🏠 Home Page</a></li>
        <li><a href="index.php?page=home">🏠 Home (explicit)</a></li>
        <li><a href="index.php?page=test">🧪 Test Page</a></li>
        <li><a href="index.php?page=login">🔐 Login</a></li>
        <li><a href="index.php?page=register">📝 Register</a></li>
        <li><a href="index.php?page=trips">🚗 Trips</a></li>
        <li><a href="index.php?page=shipments">📦 Shipments</a></li>
    </ul>
    
    <h2>📋 How It Works Now</h2>
    <p>The application now uses <strong>simple URL parameters</strong> instead of complex routing:</p>
    <ul>
        <li><code>index.php?page=home</code> - Home page</li>
        <li><code>index.php?page=login</code> - Login page</li>
        <li><code>index.php?page=register</code> - Registration</li>
        <li><code>index.php?page=dashboard</code> - User dashboard</li>
    </ul>
    
    <h2>🛠️ Next Steps</h2>
    <ol>
        <li>Click the links above to test each page</li>
        <li>Set up the database using phpMyAdmin</li>
        <li>Import the migration script: <code>scripts/migrate.sql</code></li>
        <li>Test user registration and login</li>
    </ol>
    
    <h2>🗄️ Database Setup</h2>
    <p>To set up the database:</p>
    <ol>
        <li>Open <a href="http://localhost/phpmyadmin" target="_blank">phpMyAdmin</a></li>
        <li>Create a new database called <code>tawssil_pro</code></li>
        <li>Import the file: <code>scripts/migrate.sql</code></li>
        <li>Or copy and paste the SQL content from that file</li>
    </ol>
    
    <h2>🎯 Features Available</h2>
    <ul>
        <li>✅ Home page with hero section</li>
        <li>✅ User authentication (login/register)</li>
        <li>✅ Basic dashboard</li>
        <li>✅ Trips and shipments listing</li>
        <li>✅ Multi-language support (Arabic, English, French)</li>
        <li>✅ Responsive design with Tailwind CSS</li>
    </ul>
    
    <p><strong>Start testing:</strong> <a href="index.php">Go to Main Application →</a></p>
</body>
</html>
