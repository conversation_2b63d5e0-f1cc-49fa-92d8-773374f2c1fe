<?php
class ProfileController extends Controller {
    
    public function show() {
        $this->requireAuth();
        
        $userId = $this->auth->id();
        
        $stmt = $this->db->query(
            "SELECT * FROM users WHERE id = ?",
            [$userId]
        );
        
        $user = $stmt->fetch();
        
        if (!$user) {
            $this->redirect('/dashboard');
        }
        
        // Get user statistics
        $stats = [];
        
        if ($user['role'] === 'driver') {
            // Driver stats
            $tripStmt = $this->db->query(
                "SELECT 
                    COUNT(CASE WHEN status = 'open' THEN 1 END) as active_trips,
                    COUNT(CASE WHEN status = 'full' THEN 1 END) as completed_trips,
                    COUNT(*) as total_trips
                 FROM trips WHERE user_id = ?",
                [$userId]
            );
            $stats = $tripStmt->fetch();
            
        } else {
            // Sender stats
            $shipmentStmt = $this->db->query(
                "SELECT 
                    COUNT(CASE WHEN status = 'open' THEN 1 END) as active_shipments,
                    COUNT(CASE WHEN status = 'delivered' THEN 1 END) as completed_shipments,
                    COUNT(*) as total_shipments
                 FROM shipments WHERE user_id = ?",
                [$userId]
            );
            $stats = $shipmentStmt->fetch();
        }
        
        $this->view('profile/show', compact('user', 'stats'));
    }
    
    public function edit() {
        $this->requireAuth();
        
        $userId = $this->auth->id();
        
        $stmt = $this->db->query(
            "SELECT * FROM users WHERE id = ?",
            [$userId]
        );
        
        $user = $stmt->fetch();
        
        if (!$user) {
            $this->redirect('/dashboard');
        }
        
        $this->view('profile/edit', compact('user'));
    }
    
    public function update() {
        $this->requireAuth();
        $this->validateCsrf();
        
        $userId = $this->auth->id();
        
        // Get current user data
        $stmt = $this->db->query("SELECT * FROM users WHERE id = ?", [$userId]);
        $currentUser = $stmt->fetch();
        
        if (!$currentUser) {
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => 'User not found']);
            } else {
                $this->redirect('/profile');
            }
        }
        
        $data = [
            'name' => $_POST['name'] ?? '',
            'phone' => $_POST['phone'] ?? '',
            'vehicle_type' => $_POST['vehicle_type'] ?? '',
            'vehicle_plate' => $_POST['vehicle_plate'] ?? ''
        ];
        
        // Validation
        $errors = [];
        
        if (empty($data['name'])) {
            $errors['name'] = 'Name is required';
        }
        
        // Driver-specific validation
        if ($currentUser['role'] === 'driver') {
            if (empty($data['vehicle_type'])) {
                $errors['vehicle_type'] = 'Vehicle type is required for drivers';
            }
        }
        
        // Handle avatar upload
        $avatar = $currentUser['avatar']; // Keep existing avatar
        if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
            $newAvatar = $this->uploadFile($_FILES['avatar'], 'avatar_');
            if ($newAvatar) {
                // Delete old avatar
                if ($avatar) {
                    $oldAvatarPath = Config::get('upload.path') . $avatar;
                    if (file_exists($oldAvatarPath)) {
                        unlink($oldAvatarPath);
                    }
                }
                $avatar = $newAvatar;
            } else {
                $errors['avatar'] = 'Invalid avatar file';
            }
        }
        
        // Handle password change
        $updatePassword = false;
        if (!empty($_POST['current_password']) || !empty($_POST['new_password'])) {
            if (empty($_POST['current_password'])) {
                $errors['current_password'] = 'Current password is required';
            } elseif (!password_verify($_POST['current_password'], $currentUser['password_hash'])) {
                $errors['current_password'] = 'Current password is incorrect';
            }
            
            if (empty($_POST['new_password'])) {
                $errors['new_password'] = 'New password is required';
            } elseif (strlen($_POST['new_password']) < 6) {
                $errors['new_password'] = 'New password must be at least 6 characters';
            }
            
            if ($_POST['new_password'] !== $_POST['confirm_password']) {
                $errors['confirm_password'] = 'Password confirmation does not match';
            }
            
            if (empty($errors)) {
                $updatePassword = true;
                $data['password_hash'] = password_hash($_POST['new_password'], PASSWORD_DEFAULT);
            }
        }
        
        if (!empty($errors)) {
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'errors' => $errors]);
            } else {
                $this->view('profile/edit', ['errors' => $errors, 'user' => array_merge($currentUser, $data)]);
                return;
            }
        }
        
        // Build update query
        $updateFields = ['name = ?', 'phone = ?', 'avatar = ?', 'updated_at = NOW()'];
        $updateValues = [$data['name'], $data['phone'], $avatar];
        
        if ($currentUser['role'] === 'driver') {
            $updateFields[] = 'vehicle_type = ?';
            $updateFields[] = 'vehicle_plate = ?';
            $updateValues[] = $data['vehicle_type'];
            $updateValues[] = $data['vehicle_plate'];
        }
        
        if ($updatePassword) {
            $updateFields[] = 'password_hash = ?';
            $updateValues[] = $data['password_hash'];
        }
        
        $updateValues[] = $userId;
        
        $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $this->db->query($sql, $updateValues);
        
        if ($stmt) {
            // Update session data
            $_SESSION['user']['name'] = $data['name'];
            $_SESSION['user']['avatar'] = $avatar;
            
            $message = 'Profile updated successfully';
            if ($this->isAjaxRequest()) {
                $this->json(['success' => true, 'message' => $message, 'redirect' => '/profile']);
            } else {
                $this->redirect('/profile');
            }
        } else {
            $message = 'Failed to update profile';
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => $message]);
            } else {
                $this->view('profile/edit', ['error' => $message, 'user' => array_merge($currentUser, $data)]);
            }
        }
    }
}
?>
