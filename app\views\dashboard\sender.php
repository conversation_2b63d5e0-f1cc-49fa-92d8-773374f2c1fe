<?php 
$title = $translations['sender_dashboard'] ?? 'Sender Dashboard - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';

// Get sender statistics
$userId = $auth->id();
$shipmentModel = new Shipment();
$userShipments = $shipmentModel->getByUser($userId);

$stats = [
    'active_shipments' => count(array_filter($userShipments, fn($shipment) => $shipment['status'] === 'open')),
    'matched_shipments' => count(array_filter($userShipments, fn($shipment) => $shipment['status'] === 'matched')),
    'total_shipments' => count($userShipments)
];

// Get recent offers
$offerModel = new Offer();
$recentOffers = array_slice($offerModel->getByUser($userId), 0, 5);
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">
            <?= $translations['welcome_sender'] ?? 'Welcome' ?>, <?= $_SESSION['user']['name'] ?>!
        </h1>
        <p class="text-gray-600">
            <?= $translations['sender_dashboard_subtitle'] ?? 'Manage your shipments and find reliable drivers.' ?>
        </p>
    </div>
    
    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <a href="/shipments/create" class="bg-green-600 hover:bg-green-700 text-white p-6 rounded-lg text-center transition duration-300">
            <i class="fas fa-plus-circle text-3xl mb-2"></i>
            <h3 class="text-lg font-semibold"><?= $translations['post_new_shipment'] ?? 'Post New Shipment' ?></h3>
        </a>
        
        <a href="/trips" class="bg-blue-600 hover:bg-blue-700 text-white p-6 rounded-lg text-center transition duration-300">
            <i class="fas fa-search text-3xl mb-2"></i>
            <h3 class="text-lg font-semibold"><?= $translations['find_trips'] ?? 'Find Trips' ?></h3>
        </a>
        
        <a href="/map/drivers" class="bg-purple-600 hover:bg-purple-700 text-white p-6 rounded-lg text-center transition duration-300">
            <i class="fas fa-map-marked-alt text-3xl mb-2"></i>
            <h3 class="text-lg font-semibold"><?= $translations['find_drivers'] ?? 'Find Drivers' ?></h3>
        </a>
    </div>
    
    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-box text-2xl text-green-600"></i>
                </div>
                <div class="<?= $isRTL ? 'mr-4' : 'ml-4' ?>">
                    <p class="text-sm font-medium text-gray-500"><?= $translations['active_shipments'] ?? 'Active Shipments' ?></p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['active_shipments'] ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-2xl text-blue-600"></i>
                </div>
                <div class="<?= $isRTL ? 'mr-4' : 'ml-4' ?>">
                    <p class="text-sm font-medium text-gray-500"><?= $translations['matched_shipments'] ?? 'Matched Shipments' ?></p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['matched_shipments'] ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-chart-line text-2xl text-purple-600"></i>
                </div>
                <div class="<?= $isRTL ? 'mr-4' : 'ml-4' ?>">
                    <p class="text-sm font-medium text-gray-500"><?= $translations['total_shipments'] ?? 'Total Shipments' ?></p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['total_shipments'] ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Shipments -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900"><?= $translations['recent_shipments'] ?? 'Recent Shipments' ?></h2>
                    <a href="/shipments" class="text-blue-600 hover:text-blue-500 text-sm font-medium">
                        <?= $translations['view_all'] ?? 'View All' ?>
                    </a>
                </div>
            </div>
            
            <div class="p-6">
                <?php if (empty($userShipments)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-box text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500"><?= $translations['no_shipments_yet'] ?? 'No shipments posted yet' ?></p>
                        <a href="/shipments/create" class="mt-2 inline-block text-blue-600 hover:text-blue-500">
                            <?= $translations['post_first_shipment'] ?? 'Post your first shipment' ?>
                        </a>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach (array_slice($userShipments, 0, 3) as $shipment): ?>
                            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="font-medium text-gray-900">
                                            <?= htmlspecialchars($shipment['from_city']) ?> → <?= htmlspecialchars($shipment['to_city']) ?>
                                        </h3>
                                        <p class="text-sm text-gray-500">
                                            <?= date('M d, Y', strtotime($shipment['ready_date'])) ?>
                                        </p>
                                        <p class="text-sm text-gray-600">
                                            <?= $shipment['weight_kg'] ?>kg
                                            <?php if ($shipment['fragile']): ?>
                                                • <span class="text-orange-600"><?= $translations['fragile'] ?? 'Fragile' ?></span>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full <?= 
                                        $shipment['status'] === 'open' ? 'bg-green-100 text-green-800' : 
                                        ($shipment['status'] === 'matched' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800')
                                    ?>">
                                        <?= ucfirst($shipment['status']) ?>
                                    </span>
                                </div>
                                <div class="mt-3">
                                    <a href="/shipments/<?= $shipment['id'] ?>" class="text-blue-600 hover:text-blue-500 text-sm">
                                        <?= $translations['view_details'] ?? 'View Details' ?>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Recent Offers -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900"><?= $translations['recent_offers'] ?? 'Recent Offers' ?></h2>
                    <a href="/offers" class="text-blue-600 hover:text-blue-500 text-sm font-medium">
                        <?= $translations['view_all'] ?? 'View All' ?>
                    </a>
                </div>
            </div>
            
            <div class="p-6">
                <?php if (empty($recentOffers)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-handshake text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500"><?= $translations['no_offers_yet'] ?? 'No offers made yet' ?></p>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($recentOffers as $offer): ?>
                            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="font-medium text-gray-900">
                                            <?= htmlspecialchars($offer['route']) ?>
                                        </h3>
                                        <p class="text-sm text-gray-500">
                                            <?php if ($offer['offer_type'] === 'trip_offer'): ?>
                                                <?= $translations['offer_to_driver'] ?? 'Offer to driver' ?>: <?= htmlspecialchars($offer['receiver_name']) ?>
                                            <?php else: ?>
                                                <?= $translations['driver_offer'] ?? 'Driver offer' ?>: <?= htmlspecialchars($offer['proposer_name']) ?>
                                            <?php endif; ?>
                                        </p>
                                        <?php if ($offer['price_total']): ?>
                                            <p class="text-sm text-gray-600">
                                                <?= $offer['price_total'] ?> MAD
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full <?= 
                                        $offer['status'] === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                                        ($offer['status'] === 'accepted' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800')
                                    ?>">
                                        <?= ucfirst($offer['status']) ?>
                                    </span>
                                </div>
                                <div class="mt-3">
                                    <a href="/offers/<?= $offer['id'] ?>" class="text-blue-600 hover:text-blue-500 text-sm">
                                        <?= $translations['view_offer'] ?? 'View Offer' ?>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../layout/footer.php'; ?>
