<?php
class Trip {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function findById($id) {
        $stmt = $this->db->query(
            "SELECT t.*, u.name as driver_name, u.phone as driver_phone, u.email as driver_email,
                    u.vehicle_type, u.vehicle_plate
             FROM trips t 
             JOIN users u ON t.user_id = u.id 
             WHERE t.id = ?",
            [$id]
        );
        return $stmt->fetch();
    }
    
    public function create($data) {
        $stmt = $this->db->query(
            "INSERT INTO trips (user_id, from_city, to_city, trip_date, capacity_kg, price_per_kg, notes, created_at, updated_at) 
             VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())",
            [
                $data['user_id'],
                $data['from_city'],
                $data['to_city'],
                $data['trip_date'],
                $data['capacity_kg'],
                $data['price_per_kg'],
                $data['notes']
            ]
        );
        
        return $stmt ? $this->db->lastInsertId() : false;
    }
    
    public function update($id, $data) {
        $stmt = $this->db->query(
            "UPDATE trips SET from_city = ?, to_city = ?, trip_date = ?, capacity_kg = ?, 
             price_per_kg = ?, notes = ?, updated_at = NOW()
             WHERE id = ?",
            [
                $data['from_city'],
                $data['to_city'],
                $data['trip_date'],
                $data['capacity_kg'],
                $data['price_per_kg'],
                $data['notes'],
                $id
            ]
        );
        
        return $stmt !== false;
    }
    
    public function delete($id) {
        $stmt = $this->db->query(
            "UPDATE trips SET status = 'cancelled', updated_at = NOW() WHERE id = ?",
            [$id]
        );
        
        return $stmt !== false;
    }
    
    public function search($filters = []) {
        $sql = "SELECT t.*, u.name as driver_name, u.phone as driver_phone 
                FROM trips t 
                JOIN users u ON t.user_id = u.id 
                WHERE t.status = 'open'";
        $params = [];
        
        if (!empty($filters['from_city'])) {
            $sql .= " AND t.from_city LIKE ?";
            $params[] = "%{$filters['from_city']}%";
        }
        
        if (!empty($filters['to_city'])) {
            $sql .= " AND t.to_city LIKE ?";
            $params[] = "%{$filters['to_city']}%";
        }
        
        if (!empty($filters['date'])) {
            $sql .= " AND t.trip_date >= ?";
            $params[] = $filters['date'];
        }
        
        if (!empty($filters['max_price'])) {
            $sql .= " AND t.price_per_kg <= ?";
            $params[] = $filters['max_price'];
        }
        
        $sql .= " ORDER BY t.trip_date ASC, t.created_at DESC";
        
        $stmt = $this->db->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    public function getByUser($userId) {
        $stmt = $this->db->query(
            "SELECT * FROM trips WHERE user_id = ? ORDER BY created_at DESC",
            [$userId]
        );
        return $stmt->fetchAll();
    }
    
    public function getAll($filters = []) {
        $sql = "SELECT t.*, u.name as driver_name, u.email as driver_email 
                FROM trips t 
                JOIN users u ON t.user_id = u.id";
        $params = [];
        
        if (isset($filters['status']) && $filters['status'] !== 'all') {
            $sql .= " WHERE t.status = ?";
            $params[] = $filters['status'];
        }
        
        $sql .= " ORDER BY t.created_at DESC";
        
        $stmt = $this->db->query($sql, $params);
        return $stmt->fetchAll();
    }
}
?>
