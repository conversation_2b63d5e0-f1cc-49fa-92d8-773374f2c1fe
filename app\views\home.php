<?php
$title = __('home', 'Tawssil Pro - Your Transportation Marketplace');
include __DIR__ . '/layout/header.php';
include __DIR__ . '/layout/nav.php';
?>

<!-- Hero Section -->
<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                <?= __('hero_title', 'Connect. Transport. Deliver.') ?>
            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                <?= __('hero_subtitle', 'The trusted marketplace connecting drivers and senders across Morocco for safe and efficient goods transportation.') ?>
            </p>

            <?php if (!$auth->check()): ?>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?= url('/register?role=driver') ?>" class="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold text-lg transition duration-300">
                        <?= __('join_as_driver', 'Join as Driver') ?>
                    </a>
                    <a href="<?= url('/register?role=sender') ?>" class="bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3 rounded-lg font-semibold text-lg transition duration-300">
                        <?= __('join_as_sender', 'Join as Sender') ?>
                    </a>
                </div>
            <?php else: ?>
                <a href="<?= url('/dashboard') ?>" class="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold text-lg transition duration-300 inline-block">
                    <?= __('go_to_dashboard', 'Go to Dashboard') ?>
                </a>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Search Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                <?= __('find_what_you_need', 'Find What You Need') ?>
            </h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                <?= __('search_description', 'Search for available trips or shipments that match your requirements.') ?>
            </p>
        </div>

        <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <!-- Find Trips -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-xl font-semibold mb-4 text-center">
                    <i class="fas fa-route text-blue-600 <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                    <?= __('find_trips', 'Find Trips') ?>
                </h3>

                <form action="<?= url('/trips') ?>" method="GET" class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <input type="text" name="from_city" placeholder="<?= __('from_city', 'From City') ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="text" name="to_city" placeholder="<?= __('to_city', 'To City') ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <input type="date" name="date"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="number" name="max_price" placeholder="<?= __('max_price', 'Max Price/kg') ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <button type="submit" class="w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 transition duration-300">
                        <?= __('search_trips', 'Search Trips') ?>
                    </button>
                </form>
            </div>
            
            <!-- Find Shipments -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-xl font-semibold mb-4 text-center">
                    <i class="fas fa-box text-green-600 <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                    <?= __('find_shipments', 'Find Shipments') ?>
                </h3>

                <form action="<?= url('/shipments') ?>" method="GET" class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <input type="text" name="from_city" placeholder="<?= __('from_city', 'From City') ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                        <input type="text" name="to_city" placeholder="<?= __('to_city', 'To City') ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <input type="date" name="date"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                        <input type="number" name="max_weight" placeholder="<?= __('max_weight', 'Max Weight (kg)') ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    </div>

                    <button type="submit" class="w-full bg-green-600 text-white py-2 rounded-md hover:bg-green-700 transition duration-300">
                        <?= __('search_shipments', 'Search Shipments') ?>
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                <?= __('why_choose_us', 'Why Choose Tawssil Pro?') ?>
            </h2>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-shield-alt text-blue-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2"><?= __('secure_transactions', 'Secure Transactions') ?></h3>
                <p class="text-gray-600"><?= __('secure_description', 'End-to-end security for all your transactions and communications.') ?></p>
            </div>

            <div class="text-center">
                <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-map-marked-alt text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2"><?= __('real_time_tracking', 'Real-time Tracking') ?></h3>
                <p class="text-gray-600"><?= __('tracking_description', 'Track your shipments and drivers in real-time with our integrated mapping system.') ?></p>
            </div>

            <div class="text-center">
                <div class="bg-yellow-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-users text-yellow-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2"><?= __('verified_users', 'Verified Users') ?></h3>
                <p class="text-gray-600"><?= __('verified_description', 'All users are verified to ensure safe and reliable transactions.') ?></p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-16 bg-blue-600 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">
            <?= __('ready_to_start', 'Ready to Start?') ?>
        </h2>
        <p class="text-xl mb-8 max-w-2xl mx-auto">
            <?= __('cta_description', 'Join thousands of drivers and senders already using Tawssil Pro for their transportation needs.') ?>
        </p>

        <?php if (!$auth->check()): ?>
            <a href="<?= url('/register') ?>" class="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold text-lg transition duration-300 inline-block">
                <?= __('get_started', 'Get Started Today') ?>
            </a>
        <?php else: ?>
            <div class="space-x-4 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                <?php if ($_SESSION['user']['role'] === 'driver'): ?>
                    <a href="<?= url('/trips/create') ?>" class="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold text-lg transition duration-300 inline-block">
                        <?= __('post_trip', 'Post a Trip') ?>
                    </a>
                <?php else: ?>
                    <a href="<?= url('/shipments/create') ?>" class="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold text-lg transition duration-300 inline-block">
                        <?= __('post_shipment', 'Post a Shipment') ?>
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php include __DIR__ . '/layout/footer.php'; ?>
