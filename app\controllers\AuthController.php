<?php
class AuthController extends Controller {
    
    public function showLogin() {
        if ($this->auth->check()) {
            $this->redirect('/dashboard');
        }
        
        $this->view('auth/login');
    }
    
    public function login() {
        $this->validateCsrf();
        
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        
        // Validation
        $errors = [];
        if (empty($email)) {
            $errors['email'] = 'Email is required';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Invalid email format';
        }
        
        if (empty($password)) {
            $errors['password'] = 'Password is required';
        }
        
        if (!empty($errors)) {
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'errors' => $errors]);
            } else {
                $this->view('auth/login', ['errors' => $errors]);
                return;
            }
        }
        
        if ($this->auth->login($email, $password)) {
            if ($this->isAjaxRequest()) {
                $this->json(['success' => true, 'redirect' => '/dashboard']);
            } else {
                $this->redirect('/dashboard');
            }
        } else {
            $message = 'Invalid credentials';
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'message' => $message]);
            } else {
                $this->view('auth/login', ['error' => $message]);
            }
        }
    }
    
    public function showRegister() {
        if ($this->auth->check()) {
            $this->redirect('/dashboard');
        }
        
        $this->view('auth/register');
    }
    
    public function register() {
        $this->validateCsrf();
        
        $data = [
            'name' => $_POST['name'] ?? '',
            'email' => $_POST['email'] ?? '',
            'phone' => $_POST['phone'] ?? '',
            'password' => $_POST['password'] ?? '',
            'password_confirm' => $_POST['password_confirm'] ?? '',
            'role' => $_POST['role'] ?? 'sender'
        ];
        
        // Validation
        $errors = [];
        
        if (empty($data['name'])) {
            $errors['name'] = 'Name is required';
        }
        
        if (empty($data['email'])) {
            $errors['email'] = 'Email is required';
        } elseif (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Invalid email format';
        }
        
        if (empty($data['password'])) {
            $errors['password'] = 'Password is required';
        } elseif (strlen($data['password']) < 6) {
            $errors['password'] = 'Password must be at least 6 characters';
        }
        
        if ($data['password'] !== $data['password_confirm']) {
            $errors['password_confirm'] = 'Passwords do not match';
        }
        
        if (!in_array($data['role'], ['driver', 'sender'])) {
            $errors['role'] = 'Invalid role selected';
        }
        
        if (!empty($errors)) {
            if ($this->isAjaxRequest()) {
                $this->json(['success' => false, 'errors' => $errors]);
            } else {
                $this->view('auth/register', ['errors' => $errors, 'data' => $data]);
                return;
            }
        }
        
        $result = $this->auth->register($data);
        
        if ($this->isAjaxRequest()) {
            if ($result['success']) {
                $this->json(['success' => true, 'redirect' => '/dashboard']);
            } else {
                $this->json($result);
            }
        } else {
            if ($result['success']) {
                $this->redirect('/dashboard');
            } else {
                $this->view('auth/register', ['error' => $result['message'], 'data' => $data]);
            }
        }
    }
    
    public function logout() {
        $this->auth->logout();
        $this->redirect('/');
    }
}
?>
