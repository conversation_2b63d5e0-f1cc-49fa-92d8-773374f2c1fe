# Tawssil Pro Environment Configuration
# Copy this file to .env and update the values for your environment

# Application Settings
APP_NAME="Tawssil Pro"
APP_URL=http://localhost/TawssilWeb/public
APP_DEBUG=true
APP_TIMEZONE=Africa/Casablanca

# Database Configuration (XAMPP MySQL)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=tawssil_pro
DB_USER=root
DB_PASS=

# Upload Settings
UPLOAD_MAX_SIZE=2097152
UPLOAD_PATH=public/uploads/

# Security Settings
SESSION_NAME=tawssil_session
CSRF_TOKEN_NAME=csrf_token

# Email Configuration (Optional - for notifications)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Tawssil Pro"

# Google Maps API (Optional - for location features)
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# File Storage
STORAGE_DRIVER=local
STORAGE_PATH=storage/

# Logging
LOG_LEVEL=debug
LOG_PATH=logs/

# Cache Settings
CACHE_DRIVER=file
CACHE_PATH=cache/

# Session Settings
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_FILES=storage/sessions
SESSION_CONNECTION=null
SESSION_TABLE=sessions

# Queue Settings (for background jobs)
QUEUE_DRIVER=sync

# Broadcasting (for real-time features)
BROADCAST_DRIVER=log

# Redis Configuration (if using Redis)
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Pusher Configuration (for real-time notifications)
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

# SMS Configuration (for SMS notifications)
SMS_DRIVER=log
SMS_FROM=+212600000000

# Payment Gateway (if implementing payments)
PAYMENT_GATEWAY=stripe
STRIPE_KEY=
STRIPE_SECRET=

# Social Login (Optional)
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
FACEBOOK_CLIENT_ID=
FACEBOOK_CLIENT_SECRET=

# API Rate Limiting
API_RATE_LIMIT=60
API_RATE_LIMIT_WINDOW=1

# Backup Settings
BACKUP_DRIVER=local
BACKUP_PATH=backups/

# Monitoring
MONITORING_ENABLED=false
MONITORING_URL=

# Development Tools
DEBUGBAR_ENABLED=true
TELESCOPE_ENABLED=false
