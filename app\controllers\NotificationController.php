<?php
class NotificationController extends Controller {
    
    public function index() {
        $this->requireAuth();
        
        $userId = $this->auth->id();
        
        $stmt = $this->db->query(
            "SELECT n.*, 
                    CASE n.type
                        WHEN 'new_offer' THEN 'New offer received'
                        WHEN 'offer_accepted' THEN 'Your offer was accepted'
                        WHEN 'offer_rejected' THEN 'Your offer was rejected'
                        WHEN 'offer_cancelled' THEN 'Offer was cancelled'
                        WHEN 'new_message' THEN 'New message received'
                        ELSE n.type
                    END as title
             FROM notifications n
             WHERE n.user_id = ?
             ORDER BY n.created_at DESC
             LIMIT 50",
            [$userId]
        );
        
        $notifications = $stmt->fetchAll();
        
        $this->view('notifications/index', compact('notifications'));
    }
    
    public function markRead() {
        $this->requireAuth();
        $this->validateCsrf();
        
        $notificationId = $_POST['notification_id'] ?? '';
        $userId = $this->auth->id();
        
        if ($notificationId) {
            // Mark specific notification as read
            $stmt = $this->db->query(
                "UPDATE notifications SET seen = 1 WHERE id = ? AND user_id = ?",
                [$notificationId, $userId]
            );
        } else {
            // Mark all notifications as read
            $stmt = $this->db->query(
                "UPDATE notifications SET seen = 1 WHERE user_id = ?",
                [$userId]
            );
        }
        
        if ($stmt) {
            $this->json(['success' => true, 'message' => 'Notifications marked as read']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to mark notifications as read']);
        }
    }
    
    public function getUnreadCount() {
        $this->requireAuth();
        
        $userId = $this->auth->id();
        
        $stmt = $this->db->query(
            "SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND seen = 0",
            [$userId]
        );
        
        $result = $stmt->fetch();
        
        $this->json(['count' => (int)$result['count']]);
    }
}
?>
