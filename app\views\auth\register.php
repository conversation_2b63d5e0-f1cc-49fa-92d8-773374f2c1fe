<?php 
$title = $translations['register'] ?? 'Register - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';
?>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                <?= $translations['create_account'] ?? 'Create your account' ?>
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                <?= $translations['have_account'] ?? 'Already have an account?' ?>
                <a href="/login" class="font-medium text-blue-600 hover:text-blue-500">
                    <?= $translations['login_here'] ?? 'Login here' ?>
                </a>
            </p>
        </div>
        
        <?php if (isset($error)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <form class="mt-8 space-y-6 ajax-form" action="/register" method="POST">
            <div class="space-y-4">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700">
                        <?= $translations['full_name'] ?? 'Full Name' ?>
                    </label>
                    <input id="name" name="name" type="text" required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                           placeholder="<?= $translations['name_placeholder'] ?? 'Enter your full name' ?>"
                           value="<?= htmlspecialchars($data['name'] ?? '') ?>">
                </div>
                
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">
                        <?= $translations['email'] ?? 'Email Address' ?>
                    </label>
                    <input id="email" name="email" type="email" required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                           placeholder="<?= $translations['email_placeholder'] ?? 'Enter your email' ?>"
                           value="<?= htmlspecialchars($data['email'] ?? '') ?>">
                </div>
                
                <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700">
                        <?= $translations['phone'] ?? 'Phone Number' ?>
                    </label>
                    <input id="phone" name="phone" type="tel" 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                           placeholder="<?= $translations['phone_placeholder'] ?? 'Enter your phone number' ?>"
                           value="<?= htmlspecialchars($data['phone'] ?? '') ?>">
                </div>
                
                <div>
                    <label for="role" class="block text-sm font-medium text-gray-700">
                        <?= $translations['join_as'] ?? 'Join as' ?>
                    </label>
                    <select id="role" name="role" required 
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <option value="sender" <?= ($data['role'] ?? $_GET['role'] ?? '') === 'sender' ? 'selected' : '' ?>>
                            <?= $translations['sender'] ?? 'Sender - I want to send goods' ?>
                        </option>
                        <option value="driver" <?= ($data['role'] ?? $_GET['role'] ?? '') === 'driver' ? 'selected' : '' ?>>
                            <?= $translations['driver'] ?? 'Driver - I want to transport goods' ?>
                        </option>
                    </select>
                </div>
                
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700">
                        <?= $translations['password'] ?? 'Password' ?>
                    </label>
                    <input id="password" name="password" type="password" required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                           placeholder="<?= $translations['password_placeholder'] ?? 'Enter your password' ?>">
                    <p class="mt-1 text-xs text-gray-500"><?= $translations['password_requirement'] ?? 'Minimum 6 characters' ?></p>
                </div>
                
                <div>
                    <label for="password_confirm" class="block text-sm font-medium text-gray-700">
                        <?= $translations['confirm_password'] ?? 'Confirm Password' ?>
                    </label>
                    <input id="password_confirm" name="password_confirm" type="password" required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                           placeholder="<?= $translations['confirm_password_placeholder'] ?? 'Confirm your password' ?>">
                </div>
            </div>
            
            <div class="flex items-center">
                <input id="terms" name="terms" type="checkbox" required
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="terms" class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> block text-sm text-gray-900">
                    <?= $translations['agree_terms'] ?? 'I agree to the' ?>
                    <a href="#" class="text-blue-600 hover:text-blue-500"><?= $translations['terms_and_conditions'] ?? 'Terms and Conditions' ?></a>
                </label>
            </div>
            
            <div>
                <button type="submit" 
                        data-original-text="<?= $translations['create_account'] ?? 'Create Account' ?>"
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <span class="absolute <?= $isRTL ? 'right' : 'left' ?>-0 inset-y-0 flex items-center <?= $isRTL ? 'pr' : 'pl' ?>-3">
                        <i class="fas fa-user-plus text-blue-500 group-hover:text-blue-400"></i>
                    </span>
                    <?= $translations['create_account'] ?? 'Create Account' ?>
                </button>
            </div>
        </form>
    </div>
</div>

<?php include __DIR__ . '/../layout/footer.php'; ?>
