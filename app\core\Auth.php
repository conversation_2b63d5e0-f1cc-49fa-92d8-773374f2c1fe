<?php
// Authentication and authorization helper
class Auth {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function login($email, $password) {
        $stmt = $this->db->query(
            "SELECT * FROM users WHERE email = ? AND deleted_at IS NULL",
            [$email]
        );
        
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password_hash'])) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user'] = [
                'id' => $user['id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'role' => $user['role'],
                'avatar' => $user['avatar']
            ];
            
            // Update last login
            $this->db->query(
                "UPDATE users SET updated_at = NOW() WHERE id = ?",
                [$user['id']]
            );
            
            return true;
        }
        
        return false;
    }
    
    public function register($data) {
        // Validate email uniqueness
        $stmt = $this->db->query(
            "SELECT id FROM users WHERE email = ?",
            [$data['email']]
        );
        
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'Email already exists'];
        }
        
        // Hash password
        $passwordHash = password_hash($data['password'], PASSWORD_DEFAULT);
        
        // Insert user
        $stmt = $this->db->query(
            "INSERT INTO users (name, email, phone, password_hash, role, created_at, updated_at) 
             VALUES (?, ?, ?, ?, ?, NOW(), NOW())",
            [
                $data['name'],
                $data['email'],
                $data['phone'] ?? null,
                $passwordHash,
                $data['role']
            ]
        );
        
        if ($stmt) {
            $userId = $this->db->lastInsertId();
            
            // Auto-login after registration
            $_SESSION['user_id'] = $userId;
            $_SESSION['user'] = [
                'id' => $userId,
                'name' => $data['name'],
                'email' => $data['email'],
                'role' => $data['role'],
                'avatar' => null
            ];
            
            return ['success' => true, 'message' => 'Registration successful'];
        }
        
        return ['success' => false, 'message' => 'Registration failed'];
    }
    
    public function logout() {
        session_destroy();
        return true;
    }
    
    public function check() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user']);
    }
    
    public function user() {
        return $_SESSION['user'] ?? null;
    }
    
    public function id() {
        return $_SESSION['user_id'] ?? null;
    }
    
    public function hasRole($roles) {
        if (!$this->check()) {
            return false;
        }
        
        if (is_string($roles)) {
            $roles = [$roles];
        }
        
        return in_array($_SESSION['user']['role'], $roles);
    }
    
    public function generateCsrf() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    public function validateCsrf($token) {
        return isset($_SESSION['csrf_token']) && 
               hash_equals($_SESSION['csrf_token'], $token);
    }
    
    public function isOwner($resourceUserId) {
        return $this->check() && 
               ($this->id() == $resourceUserId || $this->hasRole('admin'));
    }
}
?>
