<?php 
$title = $translations['notifications'] ?? 'Notifications - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';
?>

<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900"><?= $translations['notifications'] ?? 'Notifications' ?></h1>
                <p class="text-gray-600 mt-2"><?= $translations['stay_updated'] ?? 'Stay updated with your offers and messages' ?></p>
            </div>
            
            <?php if (!empty($notifications) && count(array_filter($notifications, fn($n) => !$n['seen'])) > 0): ?>
                <form class="ajax-form" action="/notifications/read" method="POST">
                    <button type="submit" class="text-blue-600 hover:text-blue-500 text-sm font-medium">
                        <?= $translations['mark_all_read'] ?? 'Mark All as Read' ?>
                    </button>
                </form>
            <?php endif; ?>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="bg-white rounded-lg shadow">
        <?php if (empty($notifications)): ?>
            <div class="px-6 py-12 text-center">
                <i class="fas fa-bell text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2"><?= $translations['no_notifications'] ?? 'No notifications yet' ?></h3>
                <p class="text-gray-600 mb-6"><?= $translations['no_notifications_desc'] ?? 'You\'ll receive notifications about new offers, messages, and status updates here.' ?></p>
                
                <div class="flex justify-center space-x-4 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                    <?php if ($auth->hasRole('driver')): ?>
                        <a href="/shipments" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium">
                            <?= $translations['find_shipments'] ?? 'Find Shipments' ?>
                        </a>
                    <?php else: ?>
                        <a href="/trips" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md font-medium">
                            <?= $translations['find_trips'] ?? 'Find Trips' ?>
                        </a>
                    <?php endif; ?>
                    
                    <a href="/offers" class="border border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-2 rounded-md font-medium">
                        <?= $translations['view_offers'] ?? 'View Offers' ?>
                    </a>
                </div>
            </div>
        <?php else: ?>
            <div class="divide-y divide-gray-200">
                <?php foreach ($notifications as $notification): ?>
                    <div class="px-6 py-4 <?= $notification['seen'] ? 'bg-white' : 'bg-blue-50' ?> hover:bg-gray-50 transition duration-200">
                        <div class="flex items-start">
                            <!-- Icon -->
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 rounded-full flex items-center justify-center <?= 
                                    $notification['type'] === 'new_offer' ? 'bg-green-100' : 
                                    ($notification['type'] === 'offer_accepted' ? 'bg-blue-100' : 
                                    ($notification['type'] === 'offer_rejected' ? 'bg-red-100' : 
                                    ($notification['type'] === 'new_message' ? 'bg-purple-100' : 'bg-gray-100')))
                                ?>">
                                    <?php
                                    $iconClass = 'text-gray-600';
                                    $icon = 'fas fa-bell';
                                    
                                    switch ($notification['type']) {
                                        case 'new_offer':
                                            $icon = 'fas fa-handshake';
                                            $iconClass = 'text-green-600';
                                            break;
                                        case 'offer_accepted':
                                            $icon = 'fas fa-check-circle';
                                            $iconClass = 'text-blue-600';
                                            break;
                                        case 'offer_rejected':
                                            $icon = 'fas fa-times-circle';
                                            $iconClass = 'text-red-600';
                                            break;
                                        case 'offer_cancelled':
                                            $icon = 'fas fa-ban';
                                            $iconClass = 'text-orange-600';
                                            break;
                                        case 'new_message':
                                            $icon = 'fas fa-comment';
                                            $iconClass = 'text-purple-600';
                                            break;
                                    }
                                    ?>
                                    <i class="<?= $icon ?> <?= $iconClass ?>"></i>
                                </div>
                            </div>
                            
                            <!-- Content -->
                            <div class="<?= $isRTL ? 'mr-4' : 'ml-4' ?> flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-sm font-medium text-gray-900">
                                        <?= htmlspecialchars($notification['title']) ?>
                                    </h3>
                                    
                                    <?php if (!$notification['seen']): ?>
                                        <span class="<?= $isRTL ? 'mr-2' : 'ml-2' ?> w-2 h-2 bg-blue-600 rounded-full"></span>
                                    <?php endif; ?>
                                </div>
                                
                                <p class="mt-1 text-sm text-gray-600">
                                    <?php
                                    switch ($notification['type']) {
                                        case 'new_offer':
                                            echo $translations['new_offer_desc'] ?? 'You have received a new offer for your trip/shipment.';
                                            break;
                                        case 'offer_accepted':
                                            echo $translations['offer_accepted_desc'] ?? 'Your offer has been accepted! You can now start coordinating the transport.';
                                            break;
                                        case 'offer_rejected':
                                            echo $translations['offer_rejected_desc'] ?? 'Your offer has been rejected. You can try making another offer.';
                                            break;
                                        case 'offer_cancelled':
                                            echo $translations['offer_cancelled_desc'] ?? 'An offer has been cancelled by the other party.';
                                            break;
                                        case 'new_message':
                                            echo $translations['new_message_desc'] ?? 'You have received a new message in one of your conversations.';
                                            break;
                                        default:
                                            echo $translations['notification_desc'] ?? 'You have a new notification.';
                                    }
                                    ?>
                                </p>
                                
                                <div class="mt-2 flex items-center justify-between">
                                    <span class="text-xs text-gray-500">
                                        <?php
                                        $notificationDate = strtotime($notification['created_at']);
                                        $now = time();
                                        $diff = $now - $notificationDate;
                                        
                                        if ($diff < 60) {
                                            echo $translations['just_now'] ?? 'Just now';
                                        } elseif ($diff < 3600) {
                                            $minutes = floor($diff / 60);
                                            echo $minutes . ' ' . ($translations['minutes_ago'] ?? 'minutes ago');
                                        } elseif ($diff < 86400) {
                                            $hours = floor($diff / 3600);
                                            echo $hours . ' ' . ($translations['hours_ago'] ?? 'hours ago');
                                        } else {
                                            echo date('M d, Y', $notificationDate);
                                        }
                                        ?>
                                    </span>
                                    
                                    <div class="flex space-x-2 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                                        <?php if ($notification['ref_id']): ?>
                                            <?php if ($notification['type'] === 'new_message'): ?>
                                                <a href="/messages" class="text-blue-600 hover:text-blue-500 text-xs font-medium">
                                                    <?= $translations['view_message'] ?? 'View Message' ?>
                                                </a>
                                            <?php else: ?>
                                                <a href="/offers/<?= $notification['ref_id'] ?>" class="text-blue-600 hover:text-blue-500 text-xs font-medium">
                                                    <?= $translations['view_offer'] ?? 'View Offer' ?>
                                                </a>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                        
                                        <?php if (!$notification['seen']): ?>
                                            <form class="ajax-form inline" action="/notifications/read" method="POST">
                                                <input type="hidden" name="notification_id" value="<?= $notification['id'] ?>">
                                                <button type="submit" class="text-gray-500 hover:text-gray-700 text-xs">
                                                    <?= $translations['mark_read'] ?? 'Mark as read' ?>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Notification Settings -->
    <div class="mt-8 bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['notification_settings'] ?? 'Notification Settings' ?></h3>
        
        <div class="space-y-4">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-sm font-medium text-gray-900"><?= $translations['new_offers'] ?? 'New Offers' ?></h4>
                    <p class="text-sm text-gray-600"><?= $translations['new_offers_desc'] ?? 'Get notified when you receive new offers' ?></p>
                </div>
                <div class="flex items-center">
                    <input type="checkbox" checked disabled class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                </div>
            </div>
            
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-sm font-medium text-gray-900"><?= $translations['offer_status_changes'] ?? 'Offer Status Changes' ?></h4>
                    <p class="text-sm text-gray-600"><?= $translations['offer_status_desc'] ?? 'Get notified when your offers are accepted or rejected' ?></p>
                </div>
                <div class="flex items-center">
                    <input type="checkbox" checked disabled class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                </div>
            </div>
            
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-sm font-medium text-gray-900"><?= $translations['new_messages'] ?? 'New Messages' ?></h4>
                    <p class="text-sm text-gray-600"><?= $translations['new_messages_desc'] ?? 'Get notified when you receive new messages' ?></p>
                </div>
                <div class="flex items-center">
                    <input type="checkbox" checked disabled class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                </div>
            </div>
        </div>
        
        <div class="mt-6 text-sm text-gray-500">
            <p><i class="fas fa-info-circle <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
            <?= $translations['notification_settings_note'] ?? 'Notification preferences can be customized in future updates.' ?></p>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../layout/footer.php'; ?>
