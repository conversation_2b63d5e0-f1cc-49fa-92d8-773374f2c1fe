<?php
/**
 * Helper functions for the Tawssil Pro application
 */

/**
 * Generate URL for the application
 */
function url($path = '') {
    $baseUrl = Config::get('app.url');

    // Remove trailing slash from base URL
    $baseUrl = rtrim($baseUrl, '/');

    // Ensure path starts with /
    if (!empty($path) && $path[0] !== '/') {
        $path = '/' . $path;
    }

    return $baseUrl . $path;
}

/**
 * Generate asset URL
 */
function asset($path) {
    return url('/assets/' . ltrim($path, '/'));
}

/**
 * Get translation for a given key
 */
function __($key, $default = null) {
    $lang = $_SESSION['language'] ?? Config::get('app.default_language');
    $translations = loadTranslations($lang);
    return $translations[$key] ?? $default ?? $key;
}

/**
 * Load translations for a specific language
 */
function loadTranslations($lang) {
    static $cache = [];
    
    if (!isset($cache[$lang])) {
        $translationFile = __DIR__ . "/lang/{$lang}.php";
        if (file_exists($translationFile)) {
            $cache[$lang] = include $translationFile;
        } else {
            $cache[$lang] = [];
        }
    }
    
    return $cache[$lang];
}

/**
 * Check if current language is RTL
 */
function isRTL() {
    $lang = $_SESSION['language'] ?? Config::get('app.default_language');
    return $lang === 'ar';
}

/**
 * Format price with currency
 */
function formatPrice($amount, $currency = 'MAD') {
    return number_format($amount, 2) . ' ' . $currency;
}

/**
 * Format date according to locale
 */
function formatDate($date, $format = 'M d, Y') {
    return date($format, strtotime($date));
}

/**
 * Format datetime for display
 */
function formatDateTime($datetime, $format = 'M d, Y H:i') {
    return date($format, strtotime($datetime));
}

/**
 * Get time ago string
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    $lang = $_SESSION['language'] ?? 'en';
    
    if ($time < 60) {
        return __('just_now', 'Just now');
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return $minutes . ' ' . __('minutes_ago', 'minutes ago');
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return $hours . ' ' . __('hours_ago', 'hours ago');
    } else {
        $days = floor($time / 86400);
        return $days . ' ' . __('days_ago', 'days ago');
    }
}

/**
 * Sanitize input data
 */
function sanitize($data) {
    if (is_array($data)) {
        return array_map('sanitize', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email address
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate phone number (Moroccan format)
 */
function isValidPhone($phone) {
    // Remove all non-numeric characters
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // Check if it's a valid Moroccan phone number
    return preg_match('/^(0|\+212)[5-7][0-9]{8}$/', $phone) || 
           preg_match('/^[5-7][0-9]{8}$/', $phone);
}

/**
 * Generate a random string
 */
function generateRandomString($length = 10) {
    return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )), 1, $length);
}

/**
 * Get user avatar URL
 */
function getUserAvatar($avatar = null, $size = 'md') {
    if ($avatar) {
        return '/uploads/' . $avatar;
    }
    
    $sizes = [
        'sm' => '32',
        'md' => '64',
        'lg' => '128'
    ];
    
    $avatarSize = $sizes[$size] ?? '64';
    return "https://ui-avatars.com/api/?size={$avatarSize}&background=e5e7eb&color=6b7280&name=User";
}

/**
 * Get file size in human readable format
 */
function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $size >= 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

/**
 * Check if file is a valid image
 */
function isValidImage($file) {
    $allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    return in_array($mimeType, $allowedTypes);
}

/**
 * Get distance between two coordinates
 */
function getDistance($lat1, $lon1, $lat2, $lon2, $unit = 'K') {
    if (($lat1 == $lat2) && ($lon1 == $lon2)) {
        return 0;
    }
    
    $theta = $lon1 - $lon2;
    $dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
    $dist = acos($dist);
    $dist = rad2deg($dist);
    $miles = $dist * 60 * 1.1515;
    
    switch ($unit) {
        case 'M': // Miles
            return $miles;
        case 'K': // Kilometers
            return $miles * 1.609344;
        case 'N': // Nautical miles
            return $miles * 0.8684;
        default:
            return $miles;
    }
}

/**
 * Create a slug from string
 */
function createSlug($string) {
    $string = strtolower($string);
    $string = preg_replace('/[^a-z0-9\s-]/', '', $string);
    $string = preg_replace('/[\s-]+/', '-', $string);
    return trim($string, '-');
}

/**
 * Paginate array results
 */
function paginate($items, $page = 1, $perPage = 20) {
    $total = count($items);
    $totalPages = ceil($total / $perPage);
    $offset = ($page - 1) * $perPage;
    
    return [
        'data' => array_slice($items, $offset, $perPage),
        'pagination' => [
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $total,
            'total_pages' => $totalPages,
            'has_next' => $page < $totalPages,
            'has_prev' => $page > 1
        ]
    ];
}

/**
 * Log activity
 */
function logActivity($action, $details = null, $userId = null) {
    $userId = $userId ?? ($_SESSION['user_id'] ?? null);
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'user_id' => $userId,
        'action' => $action,
        'details' => $details,
        'ip' => $ip,
        'user_agent' => $userAgent
    ];
    
    // Log to file (in production, you might want to log to database)
    $logFile = __DIR__ . '/../logs/activity.log';
    if (!file_exists(dirname($logFile))) {
        mkdir(dirname($logFile), 0755, true);
    }
    
    file_put_contents($logFile, json_encode($logEntry) . PHP_EOL, FILE_APPEND | LOCK_EX);
}

/**
 * Send notification
 */
function sendNotification($userId, $type, $refId = null) {
    try {
        $notificationModel = new Notification();
        return $notificationModel->create([
            'user_id' => $userId,
            'type' => $type,
            'ref_id' => $refId
        ]);
    } catch (Exception $e) {
        error_log('Failed to send notification: ' . $e->getMessage());
        return false;
    }
}

/**
 * Get status badge color
 */
function getStatusColor($status) {
    $colors = [
        'pending' => 'bg-yellow-100 text-yellow-800',
        'accepted' => 'bg-green-100 text-green-800',
        'rejected' => 'bg-red-100 text-red-800',
        'cancelled' => 'bg-gray-100 text-gray-800',
        'completed' => 'bg-blue-100 text-blue-800',
        'open' => 'bg-green-100 text-green-800',
        'closed' => 'bg-gray-100 text-gray-800',
        'full' => 'bg-blue-100 text-blue-800',
        'delivered' => 'bg-purple-100 text-purple-800',
        'matched' => 'bg-blue-100 text-blue-800',
        'active' => 'bg-green-100 text-green-800',
        'inactive' => 'bg-gray-100 text-gray-800'
    ];
    
    return $colors[$status] ?? 'bg-gray-100 text-gray-800';
}

/**
 * Validate CSRF token
 */
function validateCSRF($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Redirect with message
 */
function redirectWithMessage($url, $message, $type = 'success') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
    header("Location: $url");
    exit;
}

/**
 * Get flash message
 */
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}

/**
 * Calculate trip/shipment relevance score
 */
function calculateRelevanceScore($item1, $item2) {
    $score = 0;
    
    // Same route gets highest score
    if (strtolower($item1['from_city']) === strtolower($item2['from_city']) && 
        strtolower($item1['to_city']) === strtolower($item2['to_city'])) {
        $score += 100;
    }
    
    // Similar dates
    $date1 = strtotime($item1['date']);
    $date2 = strtotime($item2['date']);
    $daysDiff = abs($date1 - $date2) / (60 * 60 * 24);
    
    if ($daysDiff === 0) {
        $score += 50;
    } elseif ($daysDiff <= 3) {
        $score += 30;
    } elseif ($daysDiff <= 7) {
        $score += 10;
    }
    
    // For trips and shipments, check weight compatibility
    if (isset($item1['capacity_kg']) && isset($item2['weight_kg'])) {
        if ($item2['weight_kg'] <= $item1['capacity_kg']) {
            $score += 25;
        }
    }
    
    return $score;
}

/**
 * Format currency based on locale
 */
function formatCurrency($amount, $currency = 'MAD') {
    $lang = $_SESSION['language'] ?? 'en';
    
    switch ($lang) {
        case 'ar':
            return number_format($amount, 2) . ' درهم';
        case 'fr':
            return number_format($amount, 2, ',', ' ') . ' MAD';
        default:
            return number_format($amount, 2) . ' MAD';
    }
}

/**
 * Get moroccan cities list
 */
function getMoroccanCities() {
    return [
        'Casablanca',
        'Rabat',
        'Marrakech',
        'Fes',
        'Tangier',
        'Agadir',
        'Meknes',
        'Oujda',
        'Kenitra',
        'Tetouan',
        'Safi',
        'Mohammedia',
        'Khouribga',
        'El Jadida',
        'Beni Mellal',
        'Errachidia',
        'Taza',
        'Essaouira',
        'Ksar el Kebir',
        'Sale',
        'Bir Lehlou',
        'Arfoud',
        'Temara',
        'Tiznit',
        'Settat',
        'Larache',
        'Khemisset',
        'Guelmim',
        'Berrechid',
        'Wazzane'
    ];
}

/**
 * Validate Moroccan city name
 */
function isValidMoroccanCity($city) {
    $cities = getMoroccanCities();
    return in_array($city, $cities);
}

/**
 * Get coordinates for Moroccan cities (simplified)
 */
function getCityCoordinates($city) {
    $coordinates = [
        'Casablanca' => ['lat' => 33.5731, 'lng' => -7.5898],
        'Rabat' => ['lat' => 34.0209, 'lng' => -6.8416],
        'Marrakech' => ['lat' => 31.6295, 'lng' => -7.9811],
        'Fes' => ['lat' => 34.0181, 'lng' => -5.0078],
        'Tangier' => ['lat' => 35.7595, 'lng' => -5.8340],
        'Agadir' => ['lat' => 30.4278, 'lng' => -9.5981],
        'Meknes' => ['lat' => 33.8935, 'lng' => -5.5473],
        'Oujda' => ['lat' => 34.6814, 'lng' => -1.9086],
        'Kenitra' => ['lat' => 34.2610, 'lng' => -6.5802],
        'Tetouan' => ['lat' => 35.5889, 'lng' => -5.3626]
    ];
    
    return $coordinates[$city] ?? null;
}
