<?php 
$title = $translations['edit_profile'] ?? 'Edit Profile - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';
?>

<div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center">
            <a href="/profile" class="text-blue-600 hover:text-blue-500 <?= $isRTL ? 'ml-4' : 'mr-4' ?>">
                <i class="fas fa-arrow-<?= $isRTL ? 'right' : 'left' ?>"></i>
            </a>
            <div>
                <h1 class="text-3xl font-bold text-gray-900"><?= $translations['edit_profile'] ?? 'Edit Profile' ?></h1>
                <p class="text-gray-600 mt-2"><?= $translations['update_account_info'] ?? 'Update your account information and preferences' ?></p>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white shadow rounded-lg">
        <form class="ajax-form" action="/profile/edit" method="POST" enctype="multipart/form-data">
            <div class="px-6 py-6 space-y-8">
                <!-- Avatar Upload -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['profile_photo'] ?? 'Profile Photo' ?></h3>
                    
                    <div class="flex items-center space-x-6 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                        <div class="flex-shrink-0">
                            <?php if ($user['avatar']): ?>
                                <img src="/uploads/<?= htmlspecialchars($user['avatar']) ?>" 
                                     alt="Current avatar" 
                                     class="w-16 h-16 rounded-full object-cover"
                                     id="currentAvatar">
                            <?php else: ?>
                                <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center" id="currentAvatar">
                                    <i class="fas fa-user text-gray-500 text-xl"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="flex-1">
                            <label for="avatar" class="cursor-pointer bg-white border border-gray-300 rounded-md shadow-sm py-2 px-3 text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <?= $translations['change_photo'] ?? 'Change Photo' ?>
                                <input id="avatar" name="avatar" type="file" class="sr-only" accept="image/*" data-preview="avatarPreview">
                            </label>
                            <p class="mt-2 text-sm text-gray-500"><?= $translations['photo_requirements'] ?? 'JPG, PNG up to 2MB' ?></p>
                        </div>
                    </div>
                    
                    <!-- Avatar Preview -->
                    <div class="mt-4 hidden">
                        <img id="avatarPreview" src="#" alt="Preview" class="w-16 h-16 rounded-full object-cover">
                    </div>
                </div>

                <!-- Personal Information -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['personal_information'] ?? 'Personal Information' ?></h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['full_name'] ?? 'Full Name' ?> *
                            </label>
                            <input type="text" id="name" name="name" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   value="<?= htmlspecialchars($user['name']) ?>">
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['email'] ?? 'Email Address' ?>
                            </label>
                            <input type="email" id="email" name="email" disabled
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                                   value="<?= htmlspecialchars($user['email']) ?>">
                            <p class="mt-1 text-sm text-gray-500"><?= $translations['email_cannot_change'] ?? 'Email address cannot be changed' ?></p>
                        </div>
                        
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['phone'] ?? 'Phone Number' ?>
                            </label>
                            <input type="tel" id="phone" name="phone"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="<?= $translations['phone_placeholder'] ?? 'Enter your phone number' ?>"
                                   value="<?= htmlspecialchars($user['phone'] ?? '') ?>">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['role'] ?? 'Role' ?>
                            </label>
                            <div class="mt-1">
                                <span class="inline-flex px-3 py-2 text-sm font-medium rounded-full <?= 
                                    $user['role'] === 'driver' ? 'bg-blue-100 text-blue-800' : 
                                    ($user['role'] === 'sender' ? 'bg-green-100 text-green-800' : 'bg-purple-100 text-purple-800')
                                ?>">
                                    <?= $translations[$user['role']] ?? ucfirst($user['role']) ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Vehicle Information (for drivers only) -->
                <?php if ($user['role'] === 'driver'): ?>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['vehicle_information'] ?? 'Vehicle Information' ?></h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="vehicle_type" class="block text-sm font-medium text-gray-700 mb-2">
                                    <?= $translations['vehicle_type'] ?? 'Vehicle Type' ?> *
                                </label>
                                <select id="vehicle_type" name="vehicle_type" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value=""><?= $translations['select_vehicle_type'] ?? 'Select vehicle type' ?></option>
                                    <option value="pickup" <?= $user['vehicle_type'] === 'pickup' ? 'selected' : '' ?>><?= $translations['pickup_truck'] ?? 'Pickup Truck' ?></option>
                                    <option value="van" <?= $user['vehicle_type'] === 'van' ? 'selected' : '' ?>><?= $translations['van'] ?? 'Van' ?></option>
                                    <option value="truck" <?= $user['vehicle_type'] === 'truck' ? 'selected' : '' ?>><?= $translations['truck'] ?? 'Truck' ?></option>
                                    <option value="motorcycle" <?= $user['vehicle_type'] === 'motorcycle' ? 'selected' : '' ?>><?= $translations['motorcycle'] ?? 'Motorcycle' ?></option>
                                    <option value="car" <?= $user['vehicle_type'] === 'car' ? 'selected' : '' ?>><?= $translations['car'] ?? 'Car' ?></option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="vehicle_plate" class="block text-sm font-medium text-gray-700 mb-2">
                                    <?= $translations['vehicle_plate'] ?? 'Vehicle Plate Number' ?>
                                </label>
                                <input type="text" id="vehicle_plate" name="vehicle_plate"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="<?= $translations['plate_placeholder'] ?? 'e.g., 123456-A-12' ?>"
                                       value="<?= htmlspecialchars($user['vehicle_plate'] ?? '') ?>">
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Password Change -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['change_password'] ?? 'Change Password' ?></h3>
                    <p class="text-sm text-gray-600 mb-4"><?= $translations['password_change_note'] ?? 'Leave blank if you don\'t want to change your password' ?></p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="md:col-span-2">
                            <label for="current_password" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['current_password'] ?? 'Current Password' ?>
                            </label>
                            <input type="password" id="current_password" name="current_password"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="<?= $translations['enter_current_password'] ?? 'Enter your current password' ?>">
                        </div>
                        
                        <div>
                            <label for="new_password" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['new_password'] ?? 'New Password' ?>
                            </label>
                            <input type="password" id="new_password" name="new_password"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="<?= $translations['enter_new_password'] ?? 'Enter new password' ?>">
                            <p class="mt-1 text-sm text-gray-500"><?= $translations['password_requirement'] ?? 'Minimum 6 characters' ?></p>
                        </div>
                        
                        <div>
                            <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $translations['confirm_password'] ?? 'Confirm New Password' ?>
                            </label>
                            <input type="password" id="confirm_password" name="confirm_password"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="<?= $translations['confirm_new_password'] ?? 'Confirm new password' ?>">
                        </div>
                    </div>
                </div>

                <!-- Account Settings -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['account_settings'] ?? 'Account Settings' ?></h3>
                    
                    <div class="space-y-4">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900"><?= $translations['account_status'] ?? 'Account Status' ?></h4>
                                    <p class="text-sm text-gray-600"><?= $translations['account_active'] ?? 'Your account is currently active' ?></p>
                                </div>
                                <span class="px-3 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full">
                                    <?= $translations['active'] ?? 'Active' ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900"><?= $translations['member_since'] ?? 'Member Since' ?></h4>
                                    <p class="text-sm text-gray-600"><?= date('F j, Y', strtotime($user['created_at'])) ?></p>
                                </div>
                                <i class="fas fa-calendar text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between items-center">
                <a href="/profile" class="text-gray-600 hover:text-gray-800 font-medium">
                    <i class="fas fa-arrow-<?= $isRTL ? 'right' : 'left' ?> <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                    <?= $translations['back_to_profile'] ?? 'Back to Profile' ?>
                </a>
                
                <div class="flex space-x-3 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                    <button type="button" onclick="window.history.back()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium">
                        <?= $translations['cancel'] ?? 'Cancel' ?>
                    </button>
                    <button type="submit" 
                            data-original-text="<?= $translations['save_changes'] ?? 'Save Changes' ?>"
                            class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium transition duration-300">
                        <i class="fas fa-save <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                        <?= $translations['save_changes'] ?? 'Save Changes' ?>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Tips Section -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex items-start">
            <i class="fas fa-lightbulb text-blue-600 <?= $isRTL ? 'ml-3' : 'mr-3' ?> mt-1"></i>
            <div>
                <h3 class="text-sm font-medium text-blue-800 mb-2"><?= $translations['profile_tips'] ?? 'Profile Tips' ?></h3>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li>• <?= $translations['tip_complete_profile'] ?? 'Complete your profile to build trust with other users' ?></li>
                    <li>• <?= $translations['tip_accurate_info'] ?? 'Keep your contact information accurate and up-to-date' ?></li>
                    <?php if ($user['role'] === 'driver'): ?>
                        <li>• <?= $translations['tip_vehicle_info'] ?? 'Provide detailed vehicle information to attract more shipments' ?></li>
                    <?php endif; ?>
                    <li>• <?= $translations['tip_professional_photo'] ?? 'Use a professional profile photo to increase credibility' ?></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password field validation
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    const currentPassword = document.getElementById('current_password');
    
    function validatePasswords() {
        if (newPassword.value || confirmPassword.value || currentPassword.value) {
            newPassword.required = true;
            confirmPassword.required = true;
            currentPassword.required = true;
        } else {
            newPassword.required = false;
            confirmPassword.required = false;
            currentPassword.required = false;
        }
        
        if (newPassword.value !== confirmPassword.value && confirmPassword.value) {
            confirmPassword.setCustomValidity('Passwords do not match');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    newPassword.addEventListener('input', validatePasswords);
    confirmPassword.addEventListener('input', validatePasswords);
    currentPassword.addEventListener('input', validatePasswords);
});
</script>

<?php include __DIR__ . '/../layout/footer.php'; ?>
