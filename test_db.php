<?php
/**
 * Database Connection Test for Tawssil Pro
 * Run this file to test if the database connection is working
 */

// Load configuration
require_once __DIR__ . '/app/config/config.php';
require_once __DIR__ . '/app/config/database.php';

echo "<h1>Tawssil Pro - Database Connection Test</h1>";

try {
    // Test database connection
    $db = Database::getInstance();
    echo "<p style='color: green;'>✓ Database connection successful!</p>";
    
    // Test basic query
    $stmt = $db->query("SELECT 1 as test");
    $result = $stmt->fetch();
    
    if ($result && $result['test'] == 1) {
        echo "<p style='color: green;'>✓ Basic query test successful!</p>";
    } else {
        echo "<p style='color: red;'>✗ Basic query test failed!</p>";
    }
    
    // Check if database exists
    $stmt = $db->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?", [Config::get('database.name')]);
    $dbExists = $stmt->fetch();
    
    if ($dbExists) {
        echo "<p style='color: green;'>✓ Database '" . Config::get('database.name') . "' exists!</p>";
        
        // Check tables
        $stmt = $db->query("SHOW TABLES");
        $tables = $stmt->fetchAll();
        
        if (count($tables) > 0) {
            echo "<p style='color: green;'>✓ Found " . count($tables) . " tables in database:</p>";
            echo "<ul>";
            foreach ($tables as $table) {
                $tableName = array_values($table)[0];
                echo "<li>$tableName</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'>⚠ Database exists but no tables found. Run the migration script!</p>";
            echo "<p>To create tables, run: <code>mysql -u root -p " . Config::get('database.name') . " < scripts/migrate.sql</code></p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Database '" . Config::get('database.name') . "' does not exist!</p>";
        echo "<p>To create the database, run the migration script: <code>mysql -u root -p < scripts/migrate.sql</code></p>";
    }
    
    // Test user table if it exists
    try {
        $stmt = $db->query("SELECT COUNT(*) as count FROM users");
        $result = $stmt->fetch();
        echo "<p style='color: green;'>✓ Users table accessible. Found " . $result['count'] . " users.</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ Users table not accessible: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
    echo "<h3>Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li>Make sure XAMPP MySQL is running</li>";
    echo "<li>Check database credentials in .env file</li>";
    echo "<li>Verify database name exists</li>";
    echo "<li>Run the migration script to create tables</li>";
    echo "</ul>";
}

echo "<h3>Configuration:</h3>";
echo "<ul>";
echo "<li><strong>Host:</strong> " . Config::get('database.host') . "</li>";
echo "<li><strong>Port:</strong> " . Config::get('database.port') . "</li>";
echo "<li><strong>Database:</strong> " . Config::get('database.name') . "</li>";
echo "<li><strong>User:</strong> " . Config::get('database.user') . "</li>";
echo "<li><strong>Debug Mode:</strong> " . (Config::get('app.debug') ? 'Enabled' : 'Disabled') . "</li>";
echo "</ul>";

echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li>If database connection failed, check XAMPP MySQL service</li>";
echo "<li>If database doesn't exist, run: <code>mysql -u root -p < scripts/migrate.sql</code></li>";
echo "<li>If tables don't exist, run the migration script</li>";
echo "<li>Access the application at: <a href='" . Config::get('app.url') . "'>" . Config::get('app.url') . "</a></li>";
echo "</ol>";

echo "<p><small>Delete this file after testing for security.</small></p>";
?>
