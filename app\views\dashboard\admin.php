<?php 
$title = $translations['admin_dashboard'] ?? 'Admin Dashboard - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">
            <?= $translations['admin_dashboard'] ?? 'Admin Dashboard' ?>
        </h1>
        <p class="text-gray-600">
            <?= $translations['admin_dashboard_subtitle'] ?? 'Monitor and manage the Tawssil Pro platform.' ?>
        </p>
    </div>
    
    <!-- Statistics Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Users Stats -->
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-users text-2xl text-blue-600"></i>
                </div>
                <div class="<?= $isRTL ? 'mr-4' : 'ml-4' ?>">
                    <p class="text-sm font-medium text-gray-500"><?= $translations['total_users'] ?? 'Total Users' ?></p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['users']['total_users'] ?></p>
                    <p class="text-xs text-gray-400">
                        <?= $stats['users']['total_drivers'] ?> <?= $translations['drivers'] ?? 'drivers' ?> • 
                        <?= $stats['users']['total_senders'] ?> <?= $translations['senders'] ?? 'senders' ?>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Trips Stats -->
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-route text-2xl text-green-600"></i>
                </div>
                <div class="<?= $isRTL ? 'mr-4' : 'ml-4' ?>">
                    <p class="text-sm font-medium text-gray-500"><?= $translations['total_trips'] ?? 'Total Trips' ?></p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['trips']['total_trips'] ?></p>
                    <p class="text-xs text-gray-400">
                        <?= $stats['trips']['open_trips'] ?> <?= $translations['active'] ?? 'active' ?>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Shipments Stats -->
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-box text-2xl text-purple-600"></i>
                </div>
                <div class="<?= $isRTL ? 'mr-4' : 'ml-4' ?>">
                    <p class="text-sm font-medium text-gray-500"><?= $translations['total_shipments'] ?? 'Total Shipments' ?></p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['shipments']['total_shipments'] ?></p>
                    <p class="text-xs text-gray-400">
                        <?= $stats['shipments']['open_shipments'] ?> <?= $translations['active'] ?? 'active' ?>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Offers Stats -->
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-handshake text-2xl text-orange-600"></i>
                </div>
                <div class="<?= $isRTL ? 'mr-4' : 'ml-4' ?>">
                    <p class="text-sm font-medium text-gray-500"><?= $translations['total_offers'] ?? 'Total Offers' ?></p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['offers']['total_offers'] ?></p>
                    <p class="text-xs text-gray-400">
                        <?= $stats['offers']['pending_offers'] ?> <?= $translations['pending'] ?? 'pending' ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <a href="/admin/users" class="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg text-center transition duration-300">
            <i class="fas fa-users text-2xl mb-2"></i>
            <h3 class="font-semibold"><?= $translations['manage_users'] ?? 'Manage Users' ?></h3>
        </a>
        
        <a href="/admin/trips" class="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg text-center transition duration-300">
            <i class="fas fa-route text-2xl mb-2"></i>
            <h3 class="font-semibold"><?= $translations['manage_trips'] ?? 'Manage Trips' ?></h3>
        </a>
        
        <a href="/admin/shipments" class="bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg text-center transition duration-300">
            <i class="fas fa-box text-2xl mb-2"></i>
            <h3 class="font-semibold"><?= $translations['manage_shipments'] ?? 'Manage Shipments' ?></h3>
        </a>
        
        <a href="/admin/offers" class="bg-orange-600 hover:bg-orange-700 text-white p-4 rounded-lg text-center transition duration-300">
            <i class="fas fa-handshake text-2xl mb-2"></i>
            <h3 class="font-semibold"><?= $translations['manage_offers'] ?? 'Manage Offers' ?></h3>
        </a>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Users -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900"><?= $translations['recent_users'] ?? 'Recent Users' ?></h2>
            </div>
            
            <div class="p-6">
                <?php if (empty($recentUsers)): ?>
                    <p class="text-gray-500 text-center py-4"><?= $translations['no_recent_users'] ?? 'No recent users' ?></p>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($recentUsers as $user): ?>
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                <div>
                                    <h3 class="font-medium text-gray-900"><?= htmlspecialchars($user['name']) ?></h3>
                                    <p class="text-sm text-gray-500"><?= htmlspecialchars($user['email']) ?></p>
                                    <p class="text-xs text-gray-400">
                                        <?= ucfirst($user['role']) ?> • <?= date('M d, Y', strtotime($user['created_at'])) ?>
                                    </p>
                                </div>
                                <span class="px-2 py-1 text-xs font-semibold rounded-full <?= 
                                    $user['role'] === 'driver' ? 'bg-blue-100 text-blue-800' : 
                                    ($user['role'] === 'sender' ? 'bg-green-100 text-green-800' : 'bg-purple-100 text-purple-800')
                                ?>">
                                    <?= ucfirst($user['role']) ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Recent Trips -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900"><?= $translations['recent_trips'] ?? 'Recent Trips' ?></h2>
            </div>
            
            <div class="p-6">
                <?php if (empty($recentTrips)): ?>
                    <p class="text-gray-500 text-center py-4"><?= $translations['no_recent_trips'] ?? 'No recent trips' ?></p>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($recentTrips as $trip): ?>
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                <div>
                                    <h3 class="font-medium text-gray-900">
                                        <?= htmlspecialchars($trip['from_city']) ?> → <?= htmlspecialchars($trip['to_city']) ?>
                                    </h3>
                                    <p class="text-sm text-gray-500"><?= $translations['by'] ?? 'By' ?> <?= htmlspecialchars($trip['driver_name']) ?></p>
                                    <p class="text-xs text-gray-400">
                                        <?= date('M d, Y', strtotime($trip['trip_date'])) ?> • 
                                        <?= $trip['capacity_kg'] ?>kg • <?= $trip['price_per_kg'] ?> MAD/kg
                                    </p>
                                </div>
                                <span class="px-2 py-1 text-xs font-semibold rounded-full <?= 
                                    $trip['status'] === 'open' ? 'bg-green-100 text-green-800' : 
                                    ($trip['status'] === 'full' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800')
                                ?>">
                                    <?= ucfirst($trip['status']) ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../layout/footer.php'; ?>
