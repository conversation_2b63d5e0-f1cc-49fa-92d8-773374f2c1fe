<?php
class Message<PERSON>ontroller extends Controller {
    
    public function inbox() {
        $this->requireAuth();
        
        $userId = $this->auth->id();
        
        // Get all offers where user is involved with latest message
        $stmt = $this->db->query(
            "SELECT DISTINCT o.id as offer_id,
                    CASE 
                        WHEN o.trip_id IS NOT NULL THEN CONCAT('Trip: ', t.from_city, ' → ', t.to_city)
                        ELSE CONCAT('Shipment: ', s.from_city, ' → ', s.to_city)
                    END as subject,
                    CASE 
                        WHEN o.proposer_id = ? THEN receiver.name
                        ELSE proposer.name
                    END as other_user_name,
                    CASE 
                        WHEN o.proposer_id = ? THEN o.receiver_id
                        ELSE o.proposer_id
                    END as other_user_id,
                    (SELECT m2.body FROM messages m2 WHERE m2.offer_id = o.id ORDER BY m2.created_at DESC LIMIT 1) as last_message,
                    (SELECT m2.created_at FROM messages m2 WHERE m2.offer_id = o.id ORDER BY m2.created_at DESC LIMIT 1) as last_message_date,
                    (SELECT COUNT(*) FROM messages m3 WHERE m3.offer_id = o.id AND m3.receiver_id = ? AND m3.created_at > COALESCE((SELECT seen_at FROM message_reads WHERE user_id = ? AND offer_id = o.id), '1970-01-01')) as unread_count
             FROM offers o
             LEFT JOIN trips t ON o.trip_id = t.id
             LEFT JOIN shipments s ON o.shipment_id = s.id
             JOIN users proposer ON o.proposer_id = proposer.id
             JOIN users receiver ON o.receiver_id = receiver.id
             WHERE (o.proposer_id = ? OR o.receiver_id = ?)
             AND EXISTS (SELECT 1 FROM messages m WHERE m.offer_id = o.id)
             ORDER BY last_message_date DESC",
            [$userId, $userId, $userId, $userId, $userId, $userId]
        );
        
        $conversations = $stmt->fetchAll();
        
        $this->view('messages/inbox', compact('conversations'));
    }
    
    public function thread($offerId) {
        $this->requireAuth();
        
        $userId = $this->auth->id();
        
        // Check if user is part of this offer
        $stmt = $this->db->query(
            "SELECT o.*,
                    CASE 
                        WHEN o.trip_id IS NOT NULL THEN CONCAT('Trip: ', t.from_city, ' → ', t.to_city)
                        ELSE CONCAT('Shipment: ', s.from_city, ' → ', s.to_city)
                    END as subject,
                    proposer.name as proposer_name,
                    receiver.name as receiver_name
             FROM offers o
             LEFT JOIN trips t ON o.trip_id = t.id
             LEFT JOIN shipments s ON o.shipment_id = s.id
             JOIN users proposer ON o.proposer_id = proposer.id
             JOIN users receiver ON o.receiver_id = receiver.id
             WHERE o.id = ? AND (o.proposer_id = ? OR o.receiver_id = ?)",
            [$offerId, $userId, $userId]
        );
        
        $offer = $stmt->fetch();
        
        if (!$offer) {
            $this->redirect('/messages');
        }
        
        // Get messages
        $messagesStmt = $this->db->query(
            "SELECT m.*, u.name as sender_name, u.avatar as sender_avatar
             FROM messages m
             JOIN users u ON m.sender_id = u.id
             WHERE m.offer_id = ?
             ORDER BY m.created_at ASC",
            [$offerId]
        );
        
        $messages = $messagesStmt->fetchAll();
        
        // Mark messages as read
        $this->db->query(
            "INSERT INTO message_reads (user_id, offer_id, seen_at) 
             VALUES (?, ?, NOW()) 
             ON DUPLICATE KEY UPDATE seen_at = NOW()",
            [$userId, $offerId]
        );
        
        $this->view('messages/thread', compact('offer', 'messages'));
    }
    
    public function send() {
        $this->requireAuth();
        $this->validateCsrf();
        
        $offerId = $_POST['offer_id'] ?? '';
        $body = $_POST['body'] ?? '';
        
        // Validation
        if (empty($offerId)) {
            $this->json(['success' => false, 'message' => 'Offer ID is required']);
        }
        
        if (empty($body)) {
            $this->json(['success' => false, 'message' => 'Message body is required']);
        }
        
        $userId = $this->auth->id();
        
        // Check if user is part of this offer
        $stmt = $this->db->query(
            "SELECT * FROM offers WHERE id = ? AND (proposer_id = ? OR receiver_id = ?)",
            [$offerId, $userId, $userId]
        );
        
        $offer = $stmt->fetch();
        
        if (!$offer) {
            $this->json(['success' => false, 'message' => 'Offer not found or access denied']);
        }
        
        // Determine receiver
        $receiverId = ($offer['proposer_id'] == $userId) ? $offer['receiver_id'] : $offer['proposer_id'];
        
        // Handle attachment upload
        $attachment = null;
        if (isset($_FILES['attachment']) && $_FILES['attachment']['error'] === UPLOAD_ERR_OK) {
            $attachment = $this->uploadFile($_FILES['attachment'], 'message_');
        }
        
        // Insert message
        $stmt = $this->db->query(
            "INSERT INTO messages (offer_id, sender_id, receiver_id, body, attachment, created_at) 
             VALUES (?, ?, ?, ?, ?, NOW())",
            [$offerId, $userId, $receiverId, $body, $attachment]
        );
        
        if ($stmt) {
            $messageId = $this->db->lastInsertId();
            
            // Create notification
            $notificationModel = new Notification();
            $notificationModel->create([
                'user_id' => $receiverId,
                'type' => 'new_message',
                'ref_id' => $messageId
            ]);
            
            // Get the created message with sender info for response
            $messageStmt = $this->db->query(
                "SELECT m.*, u.name as sender_name, u.avatar as sender_avatar
                 FROM messages m
                 JOIN users u ON m.sender_id = u.id
                 WHERE m.id = ?",
                [$messageId]
            );
            
            $message = $messageStmt->fetch();
            
            $this->json([
                'success' => true,
                'message' => 'Message sent successfully',
                'data' => $message
            ]);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to send message']);
        }
    }
}
?>
