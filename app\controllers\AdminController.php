<?php
class AdminController extends Controller {
    
    public function dashboard() {
        $this->requireAuth(['admin']);
        
        // Get dashboard statistics
        $stats = [];
        
        // User statistics
        $userStmt = $this->db->query(
            "SELECT 
                COUNT(*) as total_users,
                COUNT(CASE WHEN role = 'driver' THEN 1 END) as total_drivers,
                COUNT(CASE WHEN role = 'sender' THEN 1 END) as total_senders,
                COUNT(CASE WHEN deleted_at IS NOT NULL THEN 1 END) as deleted_users
             FROM users"
        );
        $stats['users'] = $userStmt->fetch();
        
        // Trip statistics
        $tripStmt = $this->db->query(
            "SELECT 
                COUNT(*) as total_trips,
                COUNT(CASE WHEN status = 'open' THEN 1 END) as open_trips,
                COUNT(CASE WHEN status = 'full' THEN 1 END) as full_trips,
                COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_trips
             FROM trips"
        );
        $stats['trips'] = $tripStmt->fetch();
        
        // Shipment statistics
        $shipmentStmt = $this->db->query(
            "SELECT 
                COUNT(*) as total_shipments,
                COUNT(CASE WHEN status = 'open' THEN 1 END) as open_shipments,
                COUNT(CASE WHEN status = 'matched' THEN 1 END) as matched_shipments,
                COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_shipments,
                COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_shipments
             FROM shipments"
        );
        $stats['shipments'] = $shipmentStmt->fetch();
        
        // Offer statistics
        $offerStmt = $this->db->query(
            "SELECT 
                COUNT(*) as total_offers,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_offers,
                COUNT(CASE WHEN status = 'accepted' THEN 1 END) as accepted_offers,
                COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_offers,
                COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_offers
             FROM offers"
        );
        $stats['offers'] = $offerStmt->fetch();
        
        // Recent activities
        $recentUsersStmt = $this->db->query(
            "SELECT name, email, role, created_at FROM users 
             WHERE deleted_at IS NULL 
             ORDER BY created_at DESC LIMIT 10"
        );
        $recentUsers = $recentUsersStmt->fetchAll();
        
        $recentTripsStmt = $this->db->query(
            "SELECT t.*, u.name as driver_name FROM trips t
             JOIN users u ON t.user_id = u.id
             ORDER BY t.created_at DESC LIMIT 10"
        );
        $recentTrips = $recentTripsStmt->fetchAll();
        
        $this->view('dashboard/admin', compact('stats', 'recentUsers', 'recentTrips'));
    }
    
    public function users() {
        $this->requireAuth(['admin']);
        
        $search = $_GET['search'] ?? '';
        $role = $_GET['role'] ?? '';
        $status = $_GET['status'] ?? 'active';
        
        $sql = "SELECT * FROM users WHERE 1=1";
        $params = [];
        
        if ($search) {
            $sql .= " AND (name LIKE ? OR email LIKE ?)";
            $params[] = "%{$search}%";
            $params[] = "%{$search}%";
        }
        
        if ($role && $role !== 'all') {
            $sql .= " AND role = ?";
            $params[] = $role;
        }
        
        if ($status === 'active') {
            $sql .= " AND deleted_at IS NULL";
        } elseif ($status === 'deleted') {
            $sql .= " AND deleted_at IS NOT NULL";
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        $stmt = $this->db->query($sql, $params);
        $users = $stmt->fetchAll();
        
        $this->view('admin/users', compact('users', 'search', 'role', 'status'));
    }
    
    public function banUser($id) {
        $this->requireAuth(['admin']);
        $this->validateCsrf();
        
        // Cannot ban admin users
        $stmt = $this->db->query("SELECT role FROM users WHERE id = ?", [$id]);
        $user = $stmt->fetch();
        
        if (!$user) {
            $this->json(['success' => false, 'message' => 'User not found']);
        }
        
        if ($user['role'] === 'admin') {
            $this->json(['success' => false, 'message' => 'Cannot ban admin users']);
        }
        
        // Soft delete user
        $stmt = $this->db->query(
            "UPDATE users SET deleted_at = NOW() WHERE id = ?",
            [$id]
        );
        
        if ($stmt) {
            $this->json(['success' => true, 'message' => 'User banned successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to ban user']);
        }
    }
    
    public function trips() {
        $this->requireAuth(['admin']);
        
        $status = $_GET['status'] ?? 'all';
        
        $sql = "SELECT t.*, u.name as driver_name, u.email as driver_email 
                FROM trips t 
                JOIN users u ON t.user_id = u.id";
        $params = [];
        
        if ($status !== 'all') {
            $sql .= " WHERE t.status = ?";
            $params[] = $status;
        }
        
        $sql .= " ORDER BY t.created_at DESC";
        
        $stmt = $this->db->query($sql, $params);
        $trips = $stmt->fetchAll();
        
        $this->view('admin/trips', compact('trips', 'status'));
    }
    
    public function shipments() {
        $this->requireAuth(['admin']);
        
        $status = $_GET['status'] ?? 'all';
        
        $sql = "SELECT s.*, u.name as sender_name, u.email as sender_email 
                FROM shipments s 
                JOIN users u ON s.user_id = u.id";
        $params = [];
        
        if ($status !== 'all') {
            $sql .= " WHERE s.status = ?";
            $params[] = $status;
        }
        
        $sql .= " ORDER BY s.created_at DESC";
        
        $stmt = $this->db->query($sql, $params);
        $shipments = $stmt->fetchAll();
        
        $this->view('admin/shipments', compact('shipments', 'status'));
    }
    
    public function offers() {
        $this->requireAuth(['admin']);
        
        $status = $_GET['status'] ?? 'all';
        
        $sql = "SELECT o.*, 
                       proposer.name as proposer_name, proposer.email as proposer_email,
                       receiver.name as receiver_name, receiver.email as receiver_email,
                       CASE 
                           WHEN o.trip_id IS NOT NULL THEN CONCAT('Trip: ', t.from_city, ' → ', t.to_city)
                           ELSE CONCAT('Shipment: ', s.from_city, ' → ', s.to_city)
                       END as description
                FROM offers o
                LEFT JOIN trips t ON o.trip_id = t.id
                LEFT JOIN shipments s ON o.shipment_id = s.id
                JOIN users proposer ON o.proposer_id = proposer.id
                JOIN users receiver ON o.receiver_id = receiver.id";
        $params = [];
        
        if ($status !== 'all') {
            $sql .= " WHERE o.status = ?";
            $params[] = $status;
        }
        
        $sql .= " ORDER BY o.created_at DESC";
        
        $stmt = $this->db->query($sql, $params);
        $offers = $stmt->fetchAll();
        
        $this->view('admin/offers', compact('offers', 'status'));
    }
}
?>
