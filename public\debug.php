<?php
/**
 * Debug page to check routing and configuration
 */

// Load configuration
require_once __DIR__ . '/../app/config/config.php';

echo "<h1>TawssilWeb Debug Information</h1>";

echo "<h2>Server Information</h2>";
echo "<ul>";
echo "<li><strong>REQUEST_URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</li>";
echo "<li><strong>SCRIPT_NAME:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</li>";
echo "<li><strong>REQUEST_METHOD:</strong> " . ($_SERVER['REQUEST_METHOD'] ?? 'Not set') . "</li>";
echo "<li><strong>HTTP_HOST:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'Not set') . "</li>";
echo "<li><strong>DOCUMENT_ROOT:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Not set') . "</li>";
echo "</ul>";

echo "<h2>Configuration</h2>";
echo "<ul>";
echo "<li><strong>App URL:</strong> " . Config::get('app.url') . "</li>";
echo "<li><strong>App Debug:</strong> " . (Config::get('app.debug') ? 'Enabled' : 'Disabled') . "</li>";
echo "<li><strong>Database Host:</strong> " . Config::get('database.host') . "</li>";
echo "<li><strong>Database Name:</strong> " . Config::get('database.name') . "</li>";
echo "</ul>";

echo "<h2>Path Processing Test</h2>";
$requestUri = $_SERVER['REQUEST_URI'];
$path = parse_url($requestUri, PHP_URL_PATH);

echo "<ul>";
echo "<li><strong>Original URI:</strong> {$requestUri}</li>";
echo "<li><strong>Parsed Path:</strong> {$path}</li>";

// Test the path processing logic from index.php
$projectDir = '/TawssilWeb/public';
if (strpos($path, $projectDir) === 0) {
    $processedPath = substr($path, strlen($projectDir));
} else {
    $processedPath = $path;
}

if (empty($processedPath) || $processedPath[0] !== '/') {
    $processedPath = '/' . $processedPath;
}

echo "<li><strong>Processed Path:</strong> {$processedPath}</li>";
echo "</ul>";

echo "<h2>File Checks</h2>";
$files = [
    'Config' => __DIR__ . '/../app/config/config.php',
    'Database' => __DIR__ . '/../app/config/database.php',
    'Router' => __DIR__ . '/../app/core/Router.php',
    'Controller' => __DIR__ . '/../app/core/Controller.php',
    'Auth' => __DIR__ . '/../app/core/Auth.php',
    'ErrorHandler' => __DIR__ . '/../app/core/ErrorHandler.php',
    'Helpers' => __DIR__ . '/../app/helpers.php',
    '.htaccess' => __DIR__ . '/.htaccess'
];

echo "<ul>";
foreach ($files as $name => $file) {
    $exists = file_exists($file);
    $status = $exists ? '✓ Exists' : '✗ Missing';
    $color = $exists ? 'green' : 'red';
    echo "<li style='color: {$color};'><strong>{$name}:</strong> {$status}</li>";
}
echo "</ul>";

echo "<h2>Database Test</h2>";
try {
    require_once __DIR__ . '/../app/config/database.php';
    $db = Database::getInstance();
    echo "<p style='color: green;'>✓ Database connection successful!</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
}

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li>If this page loads, basic PHP is working</li>";
echo "<li>Check that the processed path shows '/' for the home page</li>";
echo "<li>Verify database connection is working</li>";
echo "<li>Try accessing: <a href='index.php'>index.php</a></li>";
echo "<li>Try accessing: <a href='../public/'>../public/</a></li>";
echo "</ol>";

echo "<p><small>Delete this file after debugging.</small></p>";
?>
