<?php
// Configuration loader
class Config {
    private static $config = [];
    
    public static function load() {
        // Load environment variables from .env file
        if (file_exists(__DIR__ . '/../../.env')) {
            $lines = file(__DIR__ . '/../../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, '=') !== false && $line[0] !== '#') {
                    list($key, $value) = explode('=', $line, 2);
                    $_ENV[trim($key)] = trim($value);
                }
            }
        }
        
        // Set default configuration
        self::$config = [
            'database' => [
                'host' => $_ENV['DB_HOST'] ?? 'localhost',
                'port' => $_ENV['DB_PORT'] ?? '3306',
                'name' => $_ENV['DB_NAME'] ?? 'tawssil_pro',
                'user' => $_ENV['DB_USER'] ?? 'root',
                'pass' => $_ENV['DB_PASS'] ?? '',
                'charset' => 'utf8mb4'
            ],
            'app' => [
                'name' => 'Tawssil Pro',
                'url' => $_ENV['APP_URL'] ?? 'http://localhost:5000',
                'debug' => $_ENV['APP_DEBUG'] ?? false,
                'timezone' => $_ENV['APP_TIMEZONE'] ?? 'Africa/Casablanca',
                'default_language' => 'ar'
            ],
            'upload' => [
                'max_size' => 2 * 1024 * 1024, // 2MB
                'allowed_types' => ['image/jpeg', 'image/png', 'image/webp'],
                'path' => __DIR__ . '/../../public/uploads/'
            ],
            'security' => [
                'csrf_token_name' => 'csrf_token',
                'session_name' => 'tawssil_session'
            ]
        ];
        
        // Set timezone
        date_default_timezone_set(self::$config['app']['timezone']);
        
        // Configure session
        ini_set('session.name', self::$config['security']['session_name']);
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
        ini_set('session.use_strict_mode', 1);
    }
    
    public static function get($key, $default = null) {
        $keys = explode('.', $key);
        $value = self::$config;
        
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }
}

// Load configuration
Config::load();
?>
