// Main application JavaScript
class TawssilApp {
    constructor() {
        this.init();
        this.setupEventListeners();
        this.startNotificationPolling();
        this.initializeMap();
        this.setupLocationTracking();
    }

    init() {
        // Initialize CSRF token
        this.csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
        
        // Initialize language and RTL
        this.currentLang = document.documentElement.lang || 'ar';
        this.isRTL = this.currentLang === 'ar';
        
        // Set initial direction
        document.dir = this.isRTL ? 'rtl' : 'ltr';
    }

    setupEventListeners() {
        // Language switcher
        document.addEventListener('change', (e) => {
            if (e.target.matches('.language-switcher')) {
                this.switchLanguage(e.target.value);
            }
        });

        // Form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.matches('.ajax-form')) {
                e.preventDefault();
                this.handleFormSubmission(e.target);
            }
        });

        // Navigation
        document.addEventListener('click', (e) => {
            if (e.target.matches('.nav-link')) {
                this.handleNavigation(e);
            }
        });

        // Modal controls
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-modal-open]')) {
                this.openModal(e.target.dataset.modalOpen);
            }
            if (e.target.matches('[data-modal-close]')) {
                this.closeModal(e.target.closest('.modal'));
            }
        });

        // File uploads
        document.addEventListener('change', (e) => {
            if (e.target.matches('input[type="file"]')) {
                this.handleFileUpload(e.target);
            }
        });

        // Search filters
        document.addEventListener('input', (e) => {
            if (e.target.matches('.search-filter')) {
                this.debounce(() => this.handleSearch(e.target), 300)();
            }
        });
    }

    async handleFormSubmission(form) {
        const formData = new FormData(form);
        formData.append('csrf_token', this.csrfToken);

        try {
            this.showLoading(form);
            
            const response = await fetch(form.action, {
                method: form.method,
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showToast(result.message, 'success');
                if (result.redirect) {
                    window.location.href = result.redirect;
                } else if (result.reload) {
                    window.location.reload();
                }
            } else {
                this.showToast(result.message, 'error');
                this.showFormErrors(form, result.errors || {});
            }
        } catch (error) {
            this.showToast('An error occurred. Please try again.', 'error');
            console.error('Form submission error:', error);
        } finally {
            this.hideLoading(form);
        }
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transform transition-transform duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'warning' ? 'bg-yellow-500 text-black' :
            'bg-blue-500 text-white'
        }`;
        toast.textContent = message;

        document.body.appendChild(toast);

        // Animate in
        setTimeout(() => toast.classList.add('translate-x-0'), 10);

        // Remove after 5 seconds
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => toast.remove(), 300);
        }, 5000);
    }

    showLoading(element) {
        const button = element.querySelector('button[type="submit"]');
        if (button) {
            button.disabled = true;
            button.innerHTML = '<svg class="animate-spin h-5 w-5 mr-2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle><path fill="currentColor" d="M4 12a8 8 0 018-8v8H4z"></path></svg>Loading...';
        }
    }

    hideLoading(element) {
        const button = element.querySelector('button[type="submit"]');
        if (button) {
            button.disabled = false;
            button.innerHTML = button.dataset.originalText || 'Submit';
        }
    }

    showFormErrors(form, errors) {
        // Clear previous errors
        form.querySelectorAll('.error-message').forEach(el => el.remove());

        Object.keys(errors).forEach(field => {
            const input = form.querySelector(`[name="${field}"]`);
            if (input) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message text-red-500 text-sm mt-1';
                errorDiv.textContent = errors[field];
                input.parentNode.appendChild(errorDiv);
            }
        });
    }

    async switchLanguage(lang) {
        const formData = new FormData();
        formData.append('lang', lang);
        formData.append('redirect', window.location.pathname);

        try {
            await fetch('/set-language', {
                method: 'POST',
                body: formData
            });
            window.location.reload();
        } catch (error) {
            console.error('Language switch error:', error);
        }
    }

    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
            modal.classList.add('flex');
            document.body.style.overflow = 'hidden';
        }
    }

    closeModal(modal) {
        if (modal) {
            modal.classList.add('hidden');
            modal.classList.remove('flex');
            document.body.style.overflow = '';
        }
    }

    handleFileUpload(input) {
        const file = input.files[0];
        if (!file) return;

        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            this.showToast('Please select a valid image file (JPEG, PNG, WebP)', 'error');
            input.value = '';
            return;
        }

        // Validate file size (2MB)
        if (file.size > 2 * 1024 * 1024) {
            this.showToast('File size must be less than 2MB', 'error');
            input.value = '';
            return;
        }

        // Show preview if preview element exists
        const preview = document.querySelector(`[data-preview="${input.name}"]`);
        if (preview) {
            const reader = new FileReader();
            reader.onload = (e) => {
                preview.src = e.target.result;
                preview.classList.remove('hidden');
            };
            reader.readAsDataURL(file);
        }
    }

    async handleSearch(input) {
        const form = input.closest('form');
        const resultsContainer = document.querySelector('[data-search-results]');
        
        if (!form || !resultsContainer) return;

        const formData = new FormData(form);
        const searchParams = new URLSearchParams();
        
        for (let [key, value] of formData.entries()) {
            if (value) searchParams.append(key, value);
        }

        try {
            const response = await fetch(`${form.action}?${searchParams}`);
            const html = await response.text();
            resultsContainer.innerHTML = html;
        } catch (error) {
            console.error('Search error:', error);
        }
    }

    async startNotificationPolling() {
        const updateNotificationCount = async () => {
            try {
                const response = await fetch('/api/notifications/unread-count');
                const data = await response.json();
                
                const badge = document.querySelector('.notification-badge');
                if (badge) {
                    if (data.count > 0) {
                        badge.textContent = data.count > 99 ? '99+' : data.count;
                        badge.classList.remove('hidden');
                    } else {
                        badge.classList.add('hidden');
                    }
                }
            } catch (error) {
                console.error('Notification polling error:', error);
            }
        };

        // Update immediately and then every 20 seconds
        updateNotificationCount();
        setInterval(updateNotificationCount, 20000);
    }

    initializeMap() {
        const mapContainer = document.getElementById('map');
        if (!mapContainer) return;

        // Initialize Leaflet map
        this.map = L.map('map').setView([33.5731, -7.5898], 6); // Morocco center

        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(this.map);

        // Custom driver icon
        this.driverIcon = L.divIcon({
            html: '<div class="driver-marker bg-blue-500 rounded-full border-2 border-white shadow-lg"><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"><path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"/><path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z"/></svg></div>',
            className: 'driver-icon',
            iconSize: [24, 24],
            iconAnchor: [12, 12]
        });

        this.loadDriverLocations();
        
        // Refresh driver locations every 30 seconds
        setInterval(() => this.loadDriverLocations(), 30000);
    }

    async loadDriverLocations() {
        if (!this.map) return;

        try {
            const response = await fetch('/api/drivers/locations');
            const drivers = await response.json();

            // Clear existing markers
            if (this.driverMarkers) {
                this.driverMarkers.forEach(marker => this.map.removeLayer(marker));
            }
            this.driverMarkers = [];

            // Add new markers
            drivers.forEach(driver => {
                if (driver.latitude && driver.longitude) {
                    const marker = L.marker([driver.latitude, driver.longitude], {
                        icon: this.driverIcon
                    }).addTo(this.map);

                    const popupContent = `
                        <div class="p-2">
                            <h3 class="font-semibold">${driver.name}</h3>
                            <p class="text-sm text-gray-600">${driver.vehicle_type}</p>
                            <p class="text-xs text-gray-500">Last updated: ${new Date(driver.updated_at).toLocaleTimeString()}</p>
                        </div>
                    `;
                    marker.bindPopup(popupContent);
                    this.driverMarkers.push(marker);
                }
            });
        } catch (error) {
            console.error('Error loading driver locations:', error);
        }
    }

    setupLocationTracking() {
        // Only track location for drivers
        const userRole = document.body.dataset.userRole;
        if (userRole !== 'driver') return;

        if ('geolocation' in navigator) {
            // Get initial position
            navigator.geolocation.getCurrentPosition(
                (position) => this.updateLocation(position),
                (error) => console.error('Geolocation error:', error),
                { enableHighAccuracy: true, timeout: 10000 }
            );

            // Watch position changes
            this.watchId = navigator.geolocation.watchPosition(
                (position) => this.updateLocation(position),
                (error) => console.error('Geolocation error:', error),
                { enableHighAccuracy: true, timeout: 10000, maximumAge: 60000 }
            );
        }
    }

    async updateLocation(position) {
        const { latitude, longitude } = position.coords;

        try {
            const formData = new FormData();
            formData.append('latitude', latitude);
            formData.append('longitude', longitude);
            formData.append('csrf_token', this.csrfToken);

            await fetch('/api/location/update', {
                method: 'POST',
                body: formData
            });
        } catch (error) {
            console.error('Location update error:', error);
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.tawssil = new TawssilApp();
});

// Service worker registration for PWA capabilities
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
                console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}
