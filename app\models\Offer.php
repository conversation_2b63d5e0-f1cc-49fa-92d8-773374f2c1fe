<?php
class Offer {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function findById($id) {
        $stmt = $this->db->query(
            "SELECT o.*, 
                    t.from_city as trip_from, t.to_city as trip_to, t.trip_date, t.capacity_kg, t.price_per_kg,
                    s.from_city as shipment_from, s.to_city as shipment_to, s.ready_date, s.weight_kg, s.description, s.fragile,
                    proposer.name as proposer_name, proposer.phone as proposer_phone, proposer.email as proposer_email,
                    receiver.name as receiver_name, receiver.phone as receiver_phone, receiver.email as receiver_email
             FROM offers o
             LEFT JOIN trips t ON o.trip_id = t.id
             LEFT JOIN shipments s ON o.shipment_id = s.id
             JOIN users proposer ON o.proposer_id = proposer.id
             JOIN users receiver ON o.receiver_id = receiver.id
             WHERE o.id = ?",
            [$id]
        );
        return $stmt->fetch();
    }
    
    public function create($data) {
        // Determine receiver based on trip or shipment
        $receiverId = null;
        
        if ($data['trip_id']) {
            $stmt = $this->db->query("SELECT user_id FROM trips WHERE id = ?", [$data['trip_id']]);
            $trip = $stmt->fetch();
            $receiverId = $trip['user_id'];
        } elseif ($data['shipment_id']) {
            $stmt = $this->db->query("SELECT user_id FROM shipments WHERE id = ?", [$data['shipment_id']]);
            $shipment = $stmt->fetch();
            $receiverId = $shipment['user_id'];
        }
        
        if (!$receiverId) {
            return false;
        }
        
        $stmt = $this->db->query(
            "INSERT INTO offers (trip_id, shipment_id, proposer_id, receiver_id, price_total, created_at, updated_at) 
             VALUES (?, ?, ?, ?, ?, NOW(), NOW())",
            [
                $data['trip_id'],
                $data['shipment_id'],
                $data['proposer_id'],
                $receiverId,
                $data['price_total']
            ]
        );
        
        return $stmt ? $this->db->lastInsertId() : false;
    }
    
    public function update($id, $data) {
        $fields = [];
        $values = [];
        
        foreach ($data as $key => $value) {
            if ($key !== 'id') {
                $fields[] = "{$key} = ?";
                $values[] = $value;
            }
        }
        
        $fields[] = "updated_at = NOW()";
        $values[] = $id;
        
        $sql = "UPDATE offers SET " . implode(', ', $fields) . " WHERE id = ?";
        $stmt = $this->db->query($sql, $values);
        
        return $stmt !== false;
    }
    
    public function getByUser($userId) {
        $stmt = $this->db->query(
            "SELECT o.*, 
                    CASE 
                        WHEN o.trip_id IS NOT NULL THEN 'trip_offer'
                        ELSE 'shipment_offer'
                    END as offer_type,
                    CASE 
                        WHEN o.trip_id IS NOT NULL THEN CONCAT(t.from_city, ' → ', t.to_city)
                        ELSE CONCAT(s.from_city, ' → ', s.to_city)
                    END as route,
                    CASE 
                        WHEN o.trip_id IS NOT NULL THEN t.trip_date
                        ELSE s.ready_date
                    END as date,
                    proposer.name as proposer_name,
                    receiver.name as receiver_name
             FROM offers o
             LEFT JOIN trips t ON o.trip_id = t.id
             LEFT JOIN shipments s ON o.shipment_id = s.id
             JOIN users proposer ON o.proposer_id = proposer.id
             JOIN users receiver ON o.receiver_id = receiver.id
             WHERE o.proposer_id = ? OR o.receiver_id = ?
             ORDER BY o.created_at DESC",
            [$userId, $userId]
        );
        
        return $stmt->fetchAll();
    }
    
    public function getAll($filters = []) {
        $sql = "SELECT o.*, 
                       proposer.name as proposer_name, proposer.email as proposer_email,
                       receiver.name as receiver_name, receiver.email as receiver_email,
                       CASE 
                           WHEN o.trip_id IS NOT NULL THEN CONCAT('Trip: ', t.from_city, ' → ', t.to_city)
                           ELSE CONCAT('Shipment: ', s.from_city, ' → ', s.to_city)
                       END as description
                FROM offers o
                LEFT JOIN trips t ON o.trip_id = t.id
                LEFT JOIN shipments s ON o.shipment_id = s.id
                JOIN users proposer ON o.proposer_id = proposer.id
                JOIN users receiver ON o.receiver_id = receiver.id";
        $params = [];
        
        if (isset($filters['status']) && $filters['status'] !== 'all') {
            $sql .= " WHERE o.status = ?";
            $params[] = $filters['status'];
        }
        
        $sql .= " ORDER BY o.created_at DESC";
        
        $stmt = $this->db->query($sql, $params);
        return $stmt->fetchAll();
    }
}
?>
