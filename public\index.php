<?php
// Front controller - handles all requests

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load configuration first (before session_start)
require_once __DIR__ . '/../app/config/config.php';
require_once __DIR__ . '/../app/helpers.php';

// Register error handler
require_once __DIR__ . '/../app/core/ErrorHandler.php';
ErrorHandler::register();

// Load database class (but don't connect yet)
require_once __DIR__ . '/../app/config/database.php';

// Configure session settings before starting
ini_set('session.name', Config::get('security.session_name'));
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_samesite', 'Strict');

// Start session after configuration is loaded
session_start();

// Auto-load classes
spl_autoload_register(function ($class) {
    $paths = [
        __DIR__ . '/../app/core/',
        __DIR__ . '/../app/controllers/',
        __DIR__ . '/../app/models/'
    ];
    
    foreach ($paths as $path) {
        $file = $path . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Initialize router
$router = new Router();

// Simple path processing for XAMPP
$requestUri = $_SERVER['REQUEST_URI'] ?? '/';
$path = parse_url($requestUri, PHP_URL_PATH);

// Remove the project directory path if present
$basePath = '/TawssilWeb/public';
if (strpos($path, $basePath) === 0) {
    $path = substr($path, strlen($basePath));
}

// Ensure path starts with /
if (empty($path)) {
    $path = '/';
} elseif ($path[0] !== '/') {
    $path = '/' . $path;
}

// Debug output
if (Config::get('app.debug')) {
    error_log("TawssilWeb Debug - Original: {$requestUri}, Processed: {$path}");
    // Also output to browser for immediate debugging
    echo "<!-- DEBUG: Original URI: {$requestUri}, Processed Path: {$path} -->\n";
}

// Override REQUEST_URI for the router to use the processed path
$_SERVER['REQUEST_URI'] = $path;

// Define routes
$router->get('/', function() {
    try {
        $controller = new Controller();
        $controller->view('home');
    } catch (Exception $e) {
        if (Config::get('app.debug')) {
            echo "<h1>Error loading home page</h1>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
            echo "<p>File: " . $e->getFile() . "</p>";
            echo "<p>Line: " . $e->getLine() . "</p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        } else {
            echo "<h1>Welcome to Tawssil Pro!</h1>";
            echo "<p>The application is running in basic mode.</p>";
        }
    }
});

// Test route
$router->get('/test', function() {
    echo "<h1>Test Route Working!</h1>";
    echo "<p>If you see this, the routing system is working correctly.</p>";
    echo "<p>Current path: " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "</p>";
    echo "<p>Debug mode: " . (Config::get('app.debug') ? 'Enabled' : 'Disabled') . "</p>";
    echo "<p><a href='" . url('/') . "'>Go to Home</a></p>";
});

// Simple hello route
$router->get('/hello', function() {
    echo "<h1>Hello from Tawssil Pro!</h1>";
    echo "<p>This is a simple test route.</p>";
});

// Auth routes
$router->get('/login', 'AuthController@showLogin');
$router->post('/login', 'AuthController@login');
$router->get('/register', 'AuthController@showRegister');
$router->post('/register', 'AuthController@register');
$router->post('/logout', 'AuthController@logout');

// Dashboard routes
$router->get('/dashboard', function() {
    $auth = new Auth();
    if (!$auth->check()) {
        header('Location: ' . url('/login'));
        exit;
    }
    
    $role = $_SESSION['user']['role'];
    $controller = new Controller();
    $controller->view("dashboard/{$role}");
});

// Trip routes
$router->get('/trips', 'TripController@index');
$router->get('/trips/create', 'TripController@create');
$router->post('/trips/create', 'TripController@store');
$router->get('/trips/{id}', 'TripController@show');
$router->get('/trips/{id}/edit', 'TripController@edit');
$router->post('/trips/{id}/edit', 'TripController@update');
$router->post('/trips/{id}/delete', 'TripController@delete');

// Shipment routes
$router->get('/shipments', 'ShipmentController@index');
$router->get('/shipments/create', 'ShipmentController@create');
$router->post('/shipments/create', 'ShipmentController@store');
$router->get('/shipments/{id}', 'ShipmentController@show');
$router->get('/shipments/{id}/edit', 'ShipmentController@edit');
$router->post('/shipments/{id}/edit', 'ShipmentController@update');
$router->post('/shipments/{id}/delete', 'ShipmentController@delete');

// Offer routes
$router->get('/offers', 'OfferController@index');
$router->get('/offers/{id}', 'OfferController@show');
$router->post('/offers/create', 'OfferController@store');
$router->post('/offers/{id}/accept', 'OfferController@accept');
$router->post('/offers/{id}/reject', 'OfferController@reject');
$router->post('/offers/{id}/cancel', 'OfferController@cancel');

// Message routes
$router->get('/messages', 'MessageController@inbox');
$router->get('/messages/thread/{id}', 'MessageController@thread');
$router->post('/messages/send', 'MessageController@send');

// Notification routes
$router->get('/notifications', 'NotificationController@index');
$router->post('/notifications/read', 'NotificationController@markRead');

// Profile routes
$router->get('/profile', 'ProfileController@show');
$router->get('/profile/edit', 'ProfileController@edit');
$router->post('/profile/edit', 'ProfileController@update');

// Map routes
$router->get('/map/drivers', function() {
    $controller = new Controller();
    $controller->view('map/drivers');
});

// API routes
$router->post('/api/auth/login', 'ApiController@login');
$router->post('/api/auth/register', 'ApiController@register');
$router->get('/api/trips', 'ApiController@searchTrips');
$router->post('/api/trips', 'ApiController@createTrip');
$router->put('/api/trips/{id}', 'ApiController@updateTrip');
$router->delete('/api/trips/{id}', 'ApiController@deleteTrip');
$router->get('/api/shipments', 'ApiController@searchShipments');
$router->post('/api/shipments', 'ApiController@createShipment');
$router->post('/api/offers', 'ApiController@createOffer');
$router->get('/api/messages/thread/{id}', 'ApiController@getMessages');
$router->post('/api/messages/send', 'ApiController@sendMessage');
$router->get('/api/notifications/unread-count', 'ApiController@getUnreadCount');
$router->post('/api/notifications/mark-read', 'ApiController@markNotificationsRead');
$router->post('/api/location/update', 'ApiController@updateLocation');
$router->get('/api/drivers/locations', 'ApiController@getDriverLocations');

// Language switcher
$router->post('/set-language', function() {
    if (isset($_POST['lang']) && in_array($_POST['lang'], ['ar', 'en', 'fr'])) {
        $_SESSION['language'] = $_POST['lang'];
    }
    header('Location: ' . ($_POST['redirect'] ?? '/'));
    exit;
});

// Admin routes
$router->get('/admin', 'AdminController@dashboard');
$router->get('/admin/users', 'AdminController@users');
$router->post('/admin/users/{id}/ban', 'AdminController@banUser');
$router->get('/admin/trips', 'AdminController@trips');
$router->get('/admin/shipments', 'AdminController@shipments');
$router->get('/admin/offers', 'AdminController@offers');

// Add a catch-all debug route if debug mode is enabled
if (Config::get('app.debug')) {
    $router->get('/debug-info', function() {
        echo "<h1>Debug Information</h1>";
        echo "<p>Current path: " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "</p>";
        echo "<p>Method: " . ($_SERVER['REQUEST_METHOD'] ?? 'Unknown') . "</p>";
        echo "<p>Original URI: " . ($_SERVER['HTTP_HOST'] ?? '') . ($_GET['original_uri'] ?? '') . "</p>";
        echo "<p>Debug mode: Enabled</p>";
        echo "<p><a href='" . url('/') . "'>Go to Home</a></p>";
    });
}

// Dispatch the request
try {
    $router->dispatch();
} catch (Exception $e) {
    if (Config::get('app.debug')) {
        echo "<h1>Router Error</h1>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "<p>File: " . $e->getFile() . "</p>";
        echo "<p>Line: " . $e->getLine() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    } else {
        echo "<h1>Application Error</h1>";
        echo "<p>Something went wrong. Please try again later.</p>";
    }
}
?>
