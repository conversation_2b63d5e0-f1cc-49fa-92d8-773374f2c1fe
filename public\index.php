<?php
// Simple front controller - basic approach

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load configuration
require_once __DIR__ . '/../app/config/config.php';
require_once __DIR__ . '/../app/helpers.php';
require_once __DIR__ . '/../app/config/database.php';

// Start session
session_start();

// Load core classes
require_once __DIR__ . '/../app/core/Auth.php';
require_once __DIR__ . '/../app/core/Controller.php';

// Get the page parameter or default to home
$page = $_GET['page'] ?? 'home';

// Initialize auth
$auth = new Auth();

// Simple routing based on page parameter
switch ($page) {
    case 'home':
    case '':
        try {
            $controller = new Controller();
            $controller->view('home');
        } catch (Exception $e) {
            echo "<h1>Welcome to Tawssil Pro!</h1>";
            echo "<p>Transportation marketplace for Morocco</p>";
            if (Config::get('app.debug')) {
                echo "<p>Error: " . $e->getMessage() . "</p>";
            }
        }
        break;

    case 'login':
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Handle login
            require_once __DIR__ . '/../app/controllers/AuthController.php';
            $controller = new AuthController();
            $controller->login();
        } else {
            // Show login form
            try {
                $controller = new Controller();
                $controller->view('auth/login');
            } catch (Exception $e) {
                echo "<h1>Login</h1>";
                echo "<form method='POST'>";
                echo "<p>Email: <input type='email' name='email' required></p>";
                echo "<p>Password: <input type='password' name='password' required></p>";
                echo "<p><button type='submit'>Login</button></p>";
                echo "</form>";
            }
        }
        break;

    case 'register':
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Handle registration
            require_once __DIR__ . '/../app/controllers/AuthController.php';
            $controller = new AuthController();
            $controller->register();
        } else {
            // Show registration form
            try {
                $controller = new Controller();
                $controller->view('auth/register');
            } catch (Exception $e) {
                echo "<h1>Register</h1>";
                echo "<form method='POST'>";
                echo "<p>Name: <input type='text' name='name' required></p>";
                echo "<p>Email: <input type='email' name='email' required></p>";
                echo "<p>Password: <input type='password' name='password' required></p>";
                echo "<p>Role: <select name='role'><option value='sender'>Sender</option><option value='driver'>Driver</option></select></p>";
                echo "<p><button type='submit'>Register</button></p>";
                echo "</form>";
            }
        }
        break;

    case 'dashboard':
        if (!$auth->check()) {
            header('Location: ?page=login');
            exit;
        }
        try {
            $role = $_SESSION['user']['role'];
            $controller = new Controller();
            $controller->view("dashboard/{$role}");
        } catch (Exception $e) {
            echo "<h1>Dashboard</h1>";
            echo "<p>Welcome, " . ($_SESSION['user']['name'] ?? 'User') . "!</p>";
            echo "<p>Role: " . ($_SESSION['user']['role'] ?? 'Unknown') . "</p>";
        }
        break;

    case 'trips':
        try {
            require_once __DIR__ . '/../app/controllers/TripController.php';
            $controller = new TripController();
            $controller->index();
        } catch (Exception $e) {
            echo "<h1>Trips</h1>";
            echo "<p>Trips functionality coming soon...</p>";
        }
        break;

    case 'shipments':
        try {
            require_once __DIR__ . '/../app/controllers/ShipmentController.php';
            $controller = new ShipmentController();
            $controller->index();
        } catch (Exception $e) {
            echo "<h1>Shipments</h1>";
            echo "<p>Shipments functionality coming soon...</p>";
        }
        break;

    case 'logout':
        $auth->logout();
        header('Location: ?page=home');
        exit;
        break;

    case 'test':
        echo "<h1>Test Page</h1>";
        echo "<p>✅ Basic routing is working!</p>";
        echo "<p>Current page: {$page}</p>";
        echo "<p>Debug mode: " . (Config::get('app.debug') ? 'ON' : 'OFF') . "</p>";
        echo "<p><a href='?page=home'>Go to Home</a></p>";
        break;

    default:
        echo "<h1>404 - Page Not Found</h1>";
        echo "<p>The page '{$page}' was not found.</p>";
        echo "<p><a href='?page=home'>Go to Home</a></p>";
        break;
}
?>
