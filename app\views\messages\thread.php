<?php 
$title = $translations['conversation'] ?? 'Conversation - Tawssil Pro';
include __DIR__ . '/../layout/header.php';
include __DIR__ . '/../layout/nav.php';
?>

<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <a href="/messages" class="text-blue-600 hover:text-blue-500 text-sm font-medium mb-2 inline-block">
                    <i class="fas fa-arrow-<?= $isRTL ? 'right' : 'left' ?> <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                    <?= $translations['back_to_messages'] ?? 'Back to Messages' ?>
                </a>
                <h1 class="text-3xl font-bold text-gray-900"><?= htmlspecialchars($offer['subject']) ?></h1>
                <p class="text-gray-600">
                    <?= $translations['conversation_with'] ?? 'Conversation with' ?> 
                    <?php if ($offer['proposer_id'] == $auth->id()): ?>
                        <?= htmlspecialchars($offer['receiver_name']) ?>
                    <?php else: ?>
                        <?= htmlspecialchars($offer['proposer_name']) ?>
                    <?php endif; ?>
                </p>
            </div>
            
            <div class="flex space-x-3 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                <a href="/offers/<?= $offer['id'] ?>" class="text-gray-600 hover:text-gray-800 text-sm font-medium">
                    <?= $translations['view_offer'] ?? 'View Offer' ?>
                </a>
            </div>
        </div>
    </div>

    <!-- Messages Container -->
    <div class="bg-white rounded-lg shadow">
        <!-- Messages List -->
        <div class="px-6 py-4 h-96 overflow-y-auto" id="messagesContainer">
            <?php if (empty($messages)): ?>
                <div class="text-center py-8">
                    <i class="fas fa-comments text-4xl text-gray-300 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2"><?= $translations['no_messages_yet'] ?? 'No messages yet' ?></h3>
                    <p class="text-gray-600"><?= $translations['start_conversation_desc'] ?? 'Send the first message to start the conversation.' ?></p>
                </div>
            <?php else: ?>
                <div class="space-y-4">
                    <?php foreach ($messages as $message): ?>
                        <div class="flex <?= $message['sender_id'] == $auth->id() ? 'justify-end' : 'justify-start' ?>">
                            <div class="max-w-xs lg:max-w-md">
                                <!-- Message Bubble -->
                                <div class="<?= $message['sender_id'] == $auth->id() ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-900' ?> rounded-lg px-4 py-3 shadow-sm">
                                    <p class="text-sm whitespace-pre-wrap"><?= nl2br(htmlspecialchars($message['body'])) ?></p>
                                    
                                    <?php if ($message['attachment']): ?>
                                        <div class="mt-2 pt-2 border-t border-opacity-20 <?= $message['sender_id'] == $auth->id() ? 'border-white' : 'border-gray-300' ?>">
                                            <a href="/uploads/<?= htmlspecialchars($message['attachment']) ?>" 
                                               target="_blank" 
                                               class="<?= $message['sender_id'] == $auth->id() ? 'text-blue-200 hover:text-white' : 'text-blue-600 hover:text-blue-800' ?> text-sm hover:underline flex items-center">
                                                <i class="fas fa-paperclip <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                                                <?= $translations['view_attachment'] ?? 'View Attachment' ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Message Info -->
                                <div class="<?= $message['sender_id'] == $auth->id() ? 'text-right' : 'text-left' ?> mt-1">
                                    <span class="text-xs text-gray-500">
                                        <?php if ($message['sender_id'] != $auth->id()): ?>
                                            <?= htmlspecialchars($message['sender_name']) ?> • 
                                        <?php endif; ?>
                                        <?= date('M d, Y \a\t H:i', strtotime($message['created_at'])) ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Message Composer -->
        <div class="border-t border-gray-200 px-6 py-4">
            <form id="messageForm" class="ajax-form" action="/messages/send" method="POST" enctype="multipart/form-data">
                <input type="hidden" name="offer_id" value="<?= $offer['id'] ?>">
                
                <div class="space-y-3">
                    <!-- Message Text -->
                    <div>
                        <textarea name="body" rows="3" required
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                                  placeholder="<?= $translations['type_message'] ?? 'Type your message...' ?>"></textarea>
                    </div>
                    
                    <!-- Attachment and Send -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3 <?= $isRTL ? 'space-x-reverse' : '' ?>">
                            <!-- File Attachment -->
                            <label for="attachment" class="cursor-pointer text-gray-500 hover:text-gray-700">
                                <i class="fas fa-paperclip text-lg"></i>
                                <input id="attachment" name="attachment" type="file" class="sr-only" accept="image/*,.pdf,.doc,.docx">
                            </label>
                            
                            <!-- Attachment Preview -->
                            <span id="attachmentName" class="text-sm text-gray-600 hidden"></span>
                        </div>
                        
                        <!-- Send Button -->
                        <button type="submit" 
                                data-original-text="<?= $translations['send'] ?? 'Send' ?>"
                                class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium transition duration-300">
                            <i class="fas fa-paper-plane <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                            <?= $translations['send'] ?? 'Send' ?>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Offer Information Sidebar -->
    <div class="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4"><?= $translations['offer_details'] ?? 'Offer Details' ?></h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Route -->
            <div>
                <span class="text-sm text-gray-500"><?= $translations['route'] ?? 'Route' ?>:</span>
                <p class="font-medium">
                    <?php if ($offer['trip_id']): ?>
                        <?= htmlspecialchars($offer['trip_from']) ?> → <?= htmlspecialchars($offer['trip_to']) ?>
                    <?php else: ?>
                        <?= htmlspecialchars($offer['shipment_from']) ?> → <?= htmlspecialchars($offer['shipment_to']) ?>
                    <?php endif; ?>
                </p>
            </div>
            
            <!-- Date -->
            <div>
                <span class="text-sm text-gray-500"><?= $translations['date'] ?? 'Date' ?>:</span>
                <p class="font-medium">
                    <?php if ($offer['trip_id']): ?>
                        <?= date('M d, Y', strtotime($offer['trip_date'])) ?>
                    <?php else: ?>
                        <?= date('M d, Y', strtotime($offer['ready_date'])) ?>
                    <?php endif; ?>
                </p>
            </div>
            
            <!-- Status -->
            <div>
                <span class="text-sm text-gray-500"><?= $translations['status'] ?? 'Status' ?>:</span>
                <p>
                    <span class="px-2 py-1 text-xs font-medium rounded-full <?= 
                        $offer['status'] === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                        ($offer['status'] === 'accepted' ? 'bg-green-100 text-green-800' : 
                        ($offer['status'] === 'rejected' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'))
                    ?>">
                        <?= $translations['status_' . $offer['status']] ?? ucfirst($offer['status']) ?>
                    </span>
                </p>
            </div>
        </div>
        
        <?php if ($offer['price_total']): ?>
            <div class="mt-4 pt-4 border-t border-gray-200">
                <span class="text-sm text-gray-500"><?= $translations['offered_price'] ?? 'Offered Price' ?>:</span>
                <p class="text-lg font-bold text-green-600"><?= $offer['price_total'] ?> MAD</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-scroll to bottom of messages
    const messagesContainer = document.getElementById('messagesContainer');
    messagesContainer.scrollTop = messagesContainer.scrollHeight;

    // Handle file attachment
    const attachmentInput = document.getElementById('attachment');
    const attachmentName = document.getElementById('attachmentName');
    
    attachmentInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            attachmentName.textContent = this.files[0].name;
            attachmentName.classList.remove('hidden');
        } else {
            attachmentName.textContent = '';
            attachmentName.classList.add('hidden');
        }
    });

    // Handle message form submission
    const messageForm = document.getElementById('messageForm');
    messageForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const submitButton = this.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        
        // Disable form
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin <?= $isRTL ? "ml-2" : "mr-2" ?>"></i><?= $translations["sending"] ?? "Sending..." ?>';
        
        fetch(this.action, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Add message to chat
                const messageData = data.data;
                addMessageToChat(messageData);
                
                // Clear form
                this.reset();
                attachmentName.textContent = '';
                attachmentName.classList.add('hidden');
                
                // Show success toast
                window.tawssil.showToast(data.message, 'success');
            } else {
                window.tawssil.showToast(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            window.tawssil.showToast('<?= $translations["error_sending_message"] ?? "Error sending message" ?>', 'error');
        })
        .finally(() => {
            // Re-enable form
            submitButton.disabled = false;
            submitButton.textContent = originalText;
        });
    });

    function addMessageToChat(messageData) {
        const messagesContainer = document.getElementById('messagesContainer');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'flex justify-end';
        
        messageDiv.innerHTML = `
            <div class="max-w-xs lg:max-w-md">
                <div class="bg-blue-600 text-white rounded-lg px-4 py-3 shadow-sm">
                    <p class="text-sm whitespace-pre-wrap">${messageData.body.replace(/\n/g, '<br>')}</p>
                    ${messageData.attachment ? `
                        <div class="mt-2 pt-2 border-t border-white border-opacity-20">
                            <a href="/uploads/${messageData.attachment}" target="_blank" 
                               class="text-blue-200 hover:text-white text-sm hover:underline flex items-center">
                                <i class="fas fa-paperclip <?= $isRTL ? 'ml-2' : 'mr-2' ?>"></i>
                                <?= $translations['view_attachment'] ?? 'View Attachment' ?>
                            </a>
                        </div>
                    ` : ''}
                </div>
                <div class="text-right mt-1">
                    <span class="text-xs text-gray-500">
                        ${new Date(messageData.created_at).toLocaleString()}
                    </span>
                </div>
            </div>
        `;
        
        // Add to messages container
        const messagesDiv = messagesContainer.querySelector('.space-y-4') || messagesContainer;
        if (messagesDiv.querySelector('.text-center')) {
            // Replace "no messages" placeholder
            messagesDiv.innerHTML = '<div class="space-y-4"></div>';
            messagesDiv = messagesDiv.querySelector('.space-y-4');
        }
        messagesDiv.appendChild(messageDiv);
        
        // Scroll to bottom
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
});
</script>

<?php include __DIR__ . '/../layout/footer.php'; ?>
