<?php
// Simple router for handling requests
class Router {
    private $routes = [];
    private $currentRoute = null;
    
    public function get($pattern, $handler) {
        $this->addRoute('GET', $pattern, $handler);
    }
    
    public function post($pattern, $handler) {
        $this->addRoute('POST', $pattern, $handler);
    }
    
    public function put($pattern, $handler) {
        $this->addRoute('PUT', $pattern, $handler);
    }
    
    public function delete($pattern, $handler) {
        $this->addRoute('DELETE', $pattern, $handler);
    }
    
    private function addRoute($method, $pattern, $handler) {
        $this->routes[] = [
            'method' => $method,
            'pattern' => $pattern,
            'handler' => $handler
        ];
    }
    
    public function dispatch() {
        $method = $_SERVER['REQUEST_METHOD'];

        // Handle PUT and DELETE via _method parameter
        if ($method === 'POST' && isset($_POST['_method'])) {
            $method = strtoupper($_POST['_method']);
        }

        $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

        // Debug output
        if (Config::get('app.debug')) {
            error_log("Router Dispatch - Method: {$method}, URI: {$uri}");
            error_log("Available routes: " . count($this->routes));
        }

        foreach ($this->routes as $route) {
            if ($route['method'] !== $method) {
                continue;
            }

            $params = $this->matchRoute($route['pattern'], $uri);
            if ($params !== false) {
                if (Config::get('app.debug')) {
                    error_log("Route matched: {$route['pattern']}");
                }
                $this->currentRoute = $route;
                $this->callHandler($route['handler'], $params);
                return;
            }
        }

        // Debug: Show all routes when 404
        if (Config::get('app.debug')) {
            error_log("No route matched for {$method} {$uri}");
            foreach ($this->routes as $route) {
                error_log("Available route: {$route['method']} {$route['pattern']}");
            }
        }

        // 404 Not Found
        http_response_code(404);
        echo "404 - Page Not Found";
    }
    
    private function matchRoute($pattern, $uri) {
        // Convert pattern to regex
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $pattern);
        $pattern = '#^' . $pattern . '$#';
        
        if (preg_match($pattern, $uri, $matches)) {
            array_shift($matches); // Remove the full match
            return $matches;
        }
        
        return false;
    }
    
    private function callHandler($handler, $params = []) {
        if (is_callable($handler)) {
            call_user_func_array($handler, $params);
        } elseif (is_string($handler)) {
            $parts = explode('@', $handler);
            if (count($parts) === 2) {
                $controllerName = $parts[0];
                $methodName = $parts[1];
                
                if (class_exists($controllerName)) {
                    $controller = new $controllerName();
                    if (method_exists($controller, $methodName)) {
                        call_user_func_array([$controller, $methodName], $params);
                    } else {
                        throw new Exception("Method {$methodName} not found in {$controllerName}");
                    }
                } else {
                    throw new Exception("Controller {$controllerName} not found");
                }
            }
        }
    }
}
?>
